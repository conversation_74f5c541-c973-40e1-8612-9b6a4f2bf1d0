"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/docs/page",{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CircleStackIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125\"\n    }));\n}\n_c = CircleStackIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CircleStackIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CircleStackIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL0NpcmNsZVN0YWNrSWNvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErQjtBQUMvQixTQUFTQyxnQkFBZ0IsS0FJeEIsRUFBRUMsTUFBTTtRQUpnQixFQUN2QkMsS0FBSyxFQUNMQyxPQUFPLEVBQ1AsR0FBR0MsT0FDSixHQUp3QjtJQUt2QixPQUFPLFdBQVcsR0FBRUwsZ0RBQW1CLENBQUMsT0FBT08sT0FBT0MsTUFBTSxDQUFDO1FBQzNEQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxhQUFhO1FBQ2JDLFFBQVE7UUFDUixlQUFlO1FBQ2YsYUFBYTtRQUNiQyxLQUFLWjtRQUNMLG1CQUFtQkU7SUFDckIsR0FBR0MsUUFBUUYsUUFBUSxXQUFXLEdBQUVILGdEQUFtQixDQUFDLFNBQVM7UUFDM0RlLElBQUlYO0lBQ04sR0FBR0QsU0FBUyxNQUFNLFdBQVcsR0FBRUgsZ0RBQW1CLENBQUMsUUFBUTtRQUN6RGdCLGVBQWU7UUFDZkMsZ0JBQWdCO1FBQ2hCQyxHQUFHO0lBQ0w7QUFDRjtLQXRCU2pCO0FBdUJULE1BQU1rQixhQUFhLFdBQVcsR0FBR25CLDZDQUFnQixDQUFDQzs7QUFDbEQsaUVBQWVrQixVQUFVQSxFQUFDIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXG5vZGVfbW9kdWxlc1xcQGhlcm9pY29uc1xccmVhY3RcXDI0XFxvdXRsaW5lXFxlc21cXENpcmNsZVN0YWNrSWNvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIENpcmNsZVN0YWNrSWNvbih7XG4gIHRpdGxlLFxuICB0aXRsZUlkLFxuICAuLi5wcm9wc1xufSwgc3ZnUmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInN2Z1wiLCBPYmplY3QuYXNzaWduKHtcbiAgICB4bWxuczogXCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiLFxuICAgIGZpbGw6IFwibm9uZVwiLFxuICAgIHZpZXdCb3g6IFwiMCAwIDI0IDI0XCIsXG4gICAgc3Ryb2tlV2lkdGg6IDEuNSxcbiAgICBzdHJva2U6IFwiY3VycmVudENvbG9yXCIsXG4gICAgXCJhcmlhLWhpZGRlblwiOiBcInRydWVcIixcbiAgICBcImRhdGEtc2xvdFwiOiBcImljb25cIixcbiAgICByZWY6IHN2Z1JlZixcbiAgICBcImFyaWEtbGFiZWxsZWRieVwiOiB0aXRsZUlkXG4gIH0sIHByb3BzKSwgdGl0bGUgPyAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInRpdGxlXCIsIHtcbiAgICBpZDogdGl0bGVJZFxuICB9LCB0aXRsZSkgOiBudWxsLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInBhdGhcIiwge1xuICAgIHN0cm9rZUxpbmVjYXA6IFwicm91bmRcIixcbiAgICBzdHJva2VMaW5lam9pbjogXCJyb3VuZFwiLFxuICAgIGQ6IFwiTTIwLjI1IDYuMzc1YzAgMi4yNzgtMy42OTQgNC4xMjUtOC4yNSA0LjEyNVMzLjc1IDguNjUzIDMuNzUgNi4zNzVtMTYuNSAwYzAtMi4yNzgtMy42OTQtNC4xMjUtOC4yNS00LjEyNVMzLjc1IDQuMDk3IDMuNzUgNi4zNzVtMTYuNSAwdjExLjI1YzAgMi4yNzgtMy42OTQgNC4xMjUtOC4yNSA0LjEyNXMtOC4yNS0xLjg0Ny04LjI1LTQuMTI1VjYuMzc1bTE2LjUgMHYzLjc1bS0xNi41LTMuNzV2My43NW0xNi41IDB2My43NUMyMC4yNSAxNi4xNTMgMTYuNTU2IDE4IDEyIDE4cy04LjI1LTEuODQ3LTguMjUtNC4xMjV2LTMuNzVtMTYuNSAwYzAgMi4yNzgtMy42OTQgNC4xMjUtOC4yNSA0LjEyNXMtOC4yNS0xLjg0Ny04LjI1LTQuMTI1XCJcbiAgfSkpO1xufVxuY29uc3QgRm9yd2FyZFJlZiA9IC8qI19fUFVSRV9fKi8gUmVhY3QuZm9yd2FyZFJlZihDaXJjbGVTdGFja0ljb24pO1xuZXhwb3J0IGRlZmF1bHQgRm9yd2FyZFJlZjsiXSwibmFtZXMiOlsiUmVhY3QiLCJDaXJjbGVTdGFja0ljb24iLCJzdmdSZWYiLCJ0aXRsZSIsInRpdGxlSWQiLCJwcm9wcyIsImNyZWF0ZUVsZW1lbnQiLCJPYmplY3QiLCJhc3NpZ24iLCJ4bWxucyIsImZpbGwiLCJ2aWV3Qm94Iiwic3Ryb2tlV2lkdGgiLCJzdHJva2UiLCJyZWYiLCJpZCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsImQiLCJGb3J3YXJkUmVmIiwiZm9yd2FyZFJlZiJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js":
/*!************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ListBulletIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n    }));\n}\n_c = ListBulletIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ListBulletIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ListBulletIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/docs/page.tsx":
/*!*******************************!*\
  !*** ./src/app/docs/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CubeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/QuestionMarkCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,CubeIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,LightBulbIcon,ListBulletIcon,PlayIcon,QuestionMarkCircleIcon,RocketLaunchIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nfunction CodeBlock(param) {\n    let { children, language = 'javascript', title } = param;\n    _s();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCopy = async ()=>{\n        await navigator.clipboard.writeText(children);\n        setCopied(true);\n        setTimeout(()=>setCopied(false), 2000);\n    };\n    // Enhanced syntax highlighting for different languages\n    const highlightSyntax = (code, lang)=>{\n        const lines = code.split('\\n');\n        return lines.map((line, index)=>{\n            let highlightedLine = line;\n            if (lang === 'javascript' || lang === 'typescript') {\n                // Keywords\n                highlightedLine = highlightedLine.replace(/\\b(const|let|var|function|async|await|import|export|from|return|if|else|for|while|try|catch|throw|new)\\b/g, '<span class=\"text-purple-400\">$1</span>');\n                // Strings\n                highlightedLine = highlightedLine.replace(/([\"'`])((?:\\\\.|(?!\\1)[^\\\\])*?)\\1/g, '<span class=\"text-green-400\">$1$2$1</span>');\n                // Comments\n                highlightedLine = highlightedLine.replace(/(\\/\\/.*$)/g, '<span class=\"text-gray-500\">$1</span>');\n                // Numbers\n                highlightedLine = highlightedLine.replace(/\\b(\\d+\\.?\\d*)\\b/g, '<span class=\"text-yellow-400\">$1</span>');\n            } else if (lang === 'python') {\n                // Python keywords\n                highlightedLine = highlightedLine.replace(/\\b(import|from|def|class|if|else|elif|for|while|try|except|with|as|return|yield|lambda|and|or|not|in|is|None|True|False)\\b/g, '<span class=\"text-purple-400\">$1</span>');\n                // Strings\n                highlightedLine = highlightedLine.replace(/([\"'`])((?:\\\\.|(?!\\1)[^\\\\])*?)\\1/g, '<span class=\"text-green-400\">$1$2$1</span>');\n                // Comments\n                highlightedLine = highlightedLine.replace(/(#.*$)/g, '<span class=\"text-gray-500\">$1</span>');\n            } else if (lang === 'bash' || lang === 'shell') {\n                // Bash commands\n                highlightedLine = highlightedLine.replace(/\\b(curl|echo|export|cd|ls|mkdir|rm|cp|mv|grep|awk|sed)\\b/g, '<span class=\"text-blue-400\">$1</span>');\n                // Flags\n                highlightedLine = highlightedLine.replace(/(-[a-zA-Z]+)/g, '<span class=\"text-yellow-400\">$1</span>');\n                // Strings\n                highlightedLine = highlightedLine.replace(/([\"'`])((?:\\\\.|(?!\\1)[^\\\\])*?)\\1/g, '<span class=\"text-green-400\">$1$2$1</span>');\n            } else if (lang === 'json') {\n                // JSON keys\n                highlightedLine = highlightedLine.replace(/\"([^\"]+)\":/g, '<span class=\"text-blue-400\">\"$1\"</span>:');\n                // JSON strings\n                highlightedLine = highlightedLine.replace(/:\\s*\"([^\"]*)\"/g, ': <span class=\"text-green-400\">\"$1\"</span>');\n                // JSON numbers\n                highlightedLine = highlightedLine.replace(/:\\s*(\\d+\\.?\\d*)/g, ': <span class=\"text-yellow-400\">$1</span>');\n                // JSON booleans\n                highlightedLine = highlightedLine.replace(/:\\s*(true|false|null)/g, ': <span class=\"text-purple-400\">$1</span>');\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"table-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"table-cell text-gray-500 text-right pr-4 select-none w-8\",\n                        children: index + 1\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"table-cell text-gray-100\",\n                        dangerouslySetInnerHTML: {\n                            __html: highlightedLine || ' '\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this);\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative bg-gray-900 rounded-xl overflow-hidden border border-gray-300 shadow-lg\",\n        children: [\n            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 px-4 py-3 text-sm text-gray-700 border-b border-gray-300 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-500 uppercase tracking-wide\",\n                        children: language\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: \"p-4 overflow-x-auto text-sm font-mono leading-relaxed\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"table w-full\",\n                            children: highlightSyntax(children, language || 'javascript')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCopy,\n                        className: \"absolute top-3 right-3 p-2 bg-white/90 hover:bg-white rounded-lg transition-all duration-200 border border-gray-300 shadow-sm\",\n                        title: \"Copy to clipboard\",\n                        children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-4 w-4 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-4 w-4 text-gray-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n_s(CodeBlock, \"NE86rL3vg4NVcTTWDavsT0hUBJs=\");\n_c = CodeBlock;\nfunction Alert(param) {\n    let { type, children } = param;\n    const styles = {\n        info: 'bg-blue-50 border-blue-200 text-blue-800',\n        warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n        tip: 'bg-green-50 border-green-200 text-green-800'\n    };\n    const iconStyles = {\n        info: 'text-blue-600',\n        warning: 'text-yellow-600',\n        tip: 'text-green-600'\n    };\n    const icons = {\n        info: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        warning: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        tip: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    };\n    const Icon = icons[type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border rounded-xl p-4 \".concat(styles[type]),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-5 w-5 flex-shrink-0 mt-0.5 \".concat(iconStyles[type])\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Alert;\nfunction DocsPage() {\n    _s1();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const sections = [\n        {\n            id: 'overview',\n            title: 'Overview',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: 'getting-started',\n            title: 'Getting Started',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: 'features',\n            title: 'Features',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: 'authentication',\n            title: 'Authentication',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: 'api-reference',\n            title: 'API Reference',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            id: 'routing-strategies',\n            title: 'Routing Strategies',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            id: 'configuration',\n            title: 'Configuration',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            id: 'use-cases',\n            title: 'Use Cases',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            id: 'examples',\n            title: 'Examples',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        },\n        {\n            id: 'future-releases',\n            title: 'Future Releases',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        },\n        {\n            id: 'faq',\n            title: 'FAQ',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-16 flex flex-col lg:flex-row min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full lg:w-80 flex-shrink-0 bg-white border-r border-gray-200 shadow-sm lg:fixed lg:top-16 lg:left-0 lg:h-[calc(100vh-4rem)] lg:z-10 lg:overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 lg:p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                            children: \"RouKey Documentation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"Complete guide to integrating and using RouKey's intelligent AI gateway\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"space-y-2\",\n                                    children: sections.map((section)=>{\n                                        const Icon = section.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveSection(section.id),\n                                            className: \"w-full flex items-center gap-3 px-4 py-3 text-sm rounded-xl transition-all duration-200 \".concat(activeSection === section.id ? 'bg-[#ff6b35] text-white shadow-lg shadow-orange-500/25' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 21\n                                                }, this),\n                                                section.title\n                                            ]\n                                        }, section.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 p-4 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 text-[#ff6b35]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: \"Quick Start\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600 mb-3\",\n                                            children: \"Get up and running in under 2 minutes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveSection('getting-started'),\n                                            className: \"w-full bg-[#ff6b35] hover:bg-[#e55a2b] text-white text-xs py-2 px-3 rounded-lg transition-colors\",\n                                            children: \"Start Building\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 bg-white text-gray-900 min-h-screen lg:ml-80\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto p-4 lg:p-8 lg:py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: [\n                                    activeSection === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"RouKey Overview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"RouKey is a commercial BYOK (Bring Your Own Keys) framework that combines multiple AI routers and a gateway to optimize your LLM API usage through intelligent routing strategies.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"What is RouKey?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"prose prose-gray max-w-none space-y-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 leading-relaxed text-lg\",\n                                                                children: \"RouKey is an intelligent AI gateway that sits between your application and multiple LLM providers (OpenAI, Anthropic, Google, DeepSeek, xAI, and more). It automatically routes requests to the most appropriate model based on your configured strategies, providing cost optimization, improved reliability, and enhanced performance.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 leading-relaxed text-lg\",\n                                                                children: \"With RouKey, you can reduce API costs by up to 60% while maintaining or improving response quality through smart routing algorithms that consider factors like prompt complexity, role requirements, and cost optimization.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"How RouKey Works\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-bold text-lg\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 315,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                                        children: \"Request Analysis\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 text-sm\",\n                                                                        children: \"RouKey analyzes incoming requests using AI classification to understand complexity, role requirements, and optimal routing strategy.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 318,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center mx-auto mb-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-bold text-lg\",\n                                                                            children: \"2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 322,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                                        children: \"Intelligent Routing\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 324,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 text-sm\",\n                                                                        children: \"Based on your configured strategy, RouKey routes the request to the optimal model considering cost, performance, and availability.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-bold text-lg\",\n                                                                            children: \"3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                                        children: \"Response Delivery\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 text-sm\",\n                                                                        children: \"RouKey delivers the response with automatic failover, retry logic, and comprehensive analytics for monitoring and optimization.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 332,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Key Benefits\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0 mt-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 342,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 text-lg mb-2\",\n                                                                                children: \"Cost Optimization\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 345,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Reduce API costs by up to 60% through intelligent routing to cost-effective models for appropriate tasks.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 351,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 text-lg mb-2\",\n                                                                                children: \"Enhanced Reliability\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 354,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Automatic failover and retry mechanisms ensure 99.9% uptime with seamless provider switching.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 355,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 353,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 360,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 text-lg mb-2\",\n                                                                                children: \"Easy Integration\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 363,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Drop-in replacement for OpenAI API with full compatibility and additional routing capabilities.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 364,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 369,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 368,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 text-lg mb-2\",\n                                                                                children: \"Enterprise Security\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 372,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Your API keys stay secure with enterprise-grade encryption and never leave your control.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 373,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 371,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-8 rounded-xl text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold mb-4\",\n                                                        children: \"Ready to optimize your AI costs?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/90 mb-6\",\n                                                        children: \"Join thousands of developers who have reduced their AI API costs by up to 60% with RouKey's intelligent routing.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveSection('getting-started'),\n                                                        className: \"bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors\",\n                                                        children: \"Get Started Now\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'getting-started' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Getting Started with RouKey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"RouKey is an intelligent AI gateway that optimizes your LLM API usage through advanced routing strategies. Get up and running in under 2 minutes.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 p-6 rounded-xl border border-[#ff6b35]/20 hover:shadow-lg hover:border-[#ff6b35]/30 transition-all duration-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-[#ff6b35]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Quick Start\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-4\",\n                                                                children: \"Get up and running with RouKey in minutes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setActiveSection('authentication'),\n                                                                className: \"text-[#ff6b35] hover:text-[#e55a2b] font-medium flex items-center gap-1 transition-colors\",\n                                                                children: [\n                                                                    \"Start here \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 36\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-blue-50 to-blue-100/50 p-6 rounded-xl border border-blue-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"API Reference\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-4\",\n                                                                children: \"Complete API documentation and examples\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setActiveSection('api-reference'),\n                                                                className: \"text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1 transition-colors\",\n                                                                children: [\n                                                                    \"View API docs \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 39\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"What is RouKey?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"prose prose-gray max-w-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 leading-relaxed text-lg\",\n                                                            children: \"RouKey is an intelligent AI gateway that sits between your application and multiple LLM providers. It automatically routes requests to the most appropriate model based on your configured strategies, providing cost optimization, improved reliability, and enhanced performance.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Key Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4 p-6 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-[#ff6b35] flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 451,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 text-lg mb-2\",\n                                                                                children: \"Intelligent Routing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 453,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"AI-powered request classification and optimal model selection\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 454,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4 p-6 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-blue-600 flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 text-lg mb-2\",\n                                                                                children: \"Multiple Strategies\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 460,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Fallback, cost-optimized, role-based, and complexity routing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 461,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4 p-6 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-green-600 flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 text-lg mb-2\",\n                                                                                children: \"Secure & Reliable\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 467,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Enterprise-grade security with automatic failover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 468,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 466,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4 p-6 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-purple-600 flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 text-lg mb-2\",\n                                                                                children: \"Cost Optimization\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 474,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Reduce costs by up to 60% with smart routing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 475,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 473,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-8 rounded-xl text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold mb-4\",\n                                                        children: \"Ready to get started?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/90 mb-6\",\n                                                        children: \"Follow our quick start guide to integrate RouKey into your application in minutes.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveSection('authentication'),\n                                                        className: \"bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors\",\n                                                        children: \"Get Started Now\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'features' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"RouKey Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"Discover the comprehensive features that make RouKey the leading AI gateway solution for developers and enterprises.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Intelligent Routing Strategies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-blue-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 512,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Intelligent Role Routing\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 mb-4\",\n                                                                        children: \"AI-powered classification routes requests based on detected roles (coding, writing, analysis, etc.) to specialized models.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 515,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-blue-600 font-medium\",\n                                                                        children: \"Best for: Multi-purpose applications\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 516,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-green-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 520,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Complexity-Based Routing\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 519,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 mb-4\",\n                                                                        children: \"Analyzes prompt complexity (1-5 scale) and routes to appropriate models for optimal cost-performance balance.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 523,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-green-600 font-medium\",\n                                                                        children: \"Best for: Cost optimization\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 524,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-[#ff6b35]\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 528,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Strict Fallback Strategy\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 527,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 mb-4\",\n                                                                        children: \"Ordered failover sequence with predictable routing and guaranteed fallback chain for maximum reliability.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 531,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-[#ff6b35] font-medium\",\n                                                                        children: \"Best for: Mission-critical applications\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 532,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_CubeIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_LightBulbIcon_ListBulletIcon_PlayIcon_QuestionMarkCircleIcon_RocketLaunchIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-purple-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 536,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Cost-Optimized Routing\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 535,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 mb-4\",\n                                                                        children: \"Smart cost optimization with learning algorithms that adapt to your usage patterns over time.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 539,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-purple-600 font-medium\",\n                                                                        children: \"Best for: Budget-conscious deployments\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 540,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Multi-Role Orchestration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-lg\",\n                                                                children: \"RouKey's advanced LangGraph integration enables sophisticated multi-role task orchestration with automatic workflow detection and management.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-6 bg-gray-50 rounded-xl border border-gray-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 mb-3\",\n                                                                                children: \"Workflow Types\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 553,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                className: \"space-y-2 text-gray-600\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: [\n                                                                                            \"• \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                                children: \"Sequential:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                                lineNumber: 555,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            \" Single roles, step-by-step processing\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 555,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: [\n                                                                                            \"• \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                                children: \"Supervisor:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                                lineNumber: 556,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            \" 2-3 roles with coordinated execution\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 556,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: [\n                                                                                            \"• \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                                children: \"Hierarchical:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                                lineNumber: 557,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            \" 4+ roles or complex browsing tasks\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 557,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: [\n                                                                                            \"• \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                                children: \"Auto:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                                lineNumber: 558,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            \" Intelligent workflow selection\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 558,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 554,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 552,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-6 bg-gray-50 rounded-xl border border-gray-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 mb-3\",\n                                                                                children: \"Advanced Features\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 562,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                className: \"space-y-2 text-gray-600\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: [\n                                                                                            \"• \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                                children: \"Memory Management:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                                lineNumber: 564,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            \" Context preservation across roles\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 564,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: [\n                                                                                            \"• \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                                children: \"Streaming Support:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                                lineNumber: 565,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            \" Real-time progress updates\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 565,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: [\n                                                                                            \"• \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                                children: \"Error Recovery:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                                lineNumber: 566,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            \" Automatic retry and fallback\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 566,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: [\n                                                                                            \"• \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                                children: \"Progress Tracking:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                                lineNumber: 567,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            \" Detailed execution monitoring\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 567,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 563,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 561,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Provider Support\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-4 bg-gray-50 rounded-xl border border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-semibold text-gray-900 mb-1\",\n                                                                        children: \"OpenAI\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 578,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"GPT-4, GPT-3.5, o1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 579,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-4 bg-gray-50 rounded-xl border border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-semibold text-gray-900 mb-1\",\n                                                                        children: \"Anthropic\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 582,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"Claude 3.5, Claude 3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 583,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-4 bg-gray-50 rounded-xl border border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-semibold text-gray-900 mb-1\",\n                                                                        children: \"Google\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 586,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"Gemini Pro, Flash\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 587,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-4 bg-gray-50 rounded-xl border border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-semibold text-gray-900 mb-1\",\n                                                                        children: \"DeepSeek\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 590,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"V2.5, Coder\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-4 bg-gray-50 rounded-xl border border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-semibold text-gray-900 mb-1\",\n                                                                        children: \"xAI\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 594,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"Grok Models\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 595,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 593,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-4 bg-gray-50 rounded-xl border border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-semibold text-gray-900 mb-1\",\n                                                                        children: \"OpenRouter\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 598,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"300+ Models\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 599,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mt-6 text-center\",\n                                                        children: [\n                                                            \"Access to \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"300+ AI models\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 33\n                                                            }, this),\n                                                            \" from leading providers with unified API interface\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Advanced Capabilities\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                        children: \"Semantic Caching\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 611,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 mb-4\",\n                                                                        children: \"Intelligent caching with Jina embeddings and reranking for faster responses and reduced costs.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 612,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"space-y-2 text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Jina embedding-based similarity detection\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 616,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Multiple API key rotation (9+ keys supported)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 617,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• jina-reranker-m0 for result optimization\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 618,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Automatic cache invalidation\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 619,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 615,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 610,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                        children: \"Knowledge Base Integration\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 623,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 mb-4\",\n                                                                        children: \"Upload and integrate custom documents for enhanced AI responses with your specific knowledge.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 624,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"space-y-2 text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Document upload and processing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 628,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Automatic embedding generation\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 629,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Context-aware retrieval\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 630,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Tier-based document limits\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 631,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                        children: \"Custom Training\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 635,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 mb-4\",\n                                                                        children: \"Train models with your specific data and requirements for specialized use cases.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 636,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"space-y-2 text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• File upload and processing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 640,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Training job management\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 641,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Enhanced system prompts\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 642,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Multi-provider compatibility\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 643,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 639,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                        children: \"Async Processing\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 647,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 mb-4\",\n                                                                        children: \"Handle long-running tasks with asynchronous processing and webhook notifications.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 648,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"space-y-2 text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Job submission and tracking\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 652,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Webhook notifications\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 653,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Progress monitoring\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 654,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Result retrieval\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 655,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 651,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 646,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'authentication' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Authentication\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"Learn how to authenticate with RouKey using API keys.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                type: \"info\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Important:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" RouKey uses the \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-blue-100 px-2 py-1 rounded text-blue-800\",\n                                                        children: \"X-API-Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    \" header for authentication. Never use \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-blue-100 px-2 py-1 rounded text-blue-800\",\n                                                        children: \"Authorization\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 31\n                                                    }, this),\n                                                    \" header format.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Getting Your API Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-lg\",\n                                                                children: \"To get started with RouKey, you'll need to create an API key from your dashboard:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 680,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                className: \"list-decimal list-inside space-y-3 text-gray-600 ml-4 text-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            \"Sign up for a RouKey account at \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                href: \"https://roukey.online\",\n                                                                                className: \"text-[#ff6b35] hover:text-[#e55a2b] underline\",\n                                                                                children: \"roukey.online\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 684,\n                                                                                columnNumber: 61\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 684,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Navigate to your dashboard and create a configuration\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 685,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Add your LLM provider API keys (OpenAI, Anthropic, etc.)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 686,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Generate a user API key for external access\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 687,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            \"Copy your API key (format: \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                className: \"bg-gray-100 px-2 py-1 rounded text-gray-700\",\n                                                                                children: \"rk_live_...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 688,\n                                                                                columnNumber: 56\n                                                                            }, this),\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 688,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Authentication Methods\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                        children: \"Method 1: X-API-Key Header (Recommended)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 697,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                                        title: \"Using X-API-Key header\",\n                                                                        language: \"bash\",\n                                                                        children: 'curl -X POST \"https://roukey.online/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"X-API-Key: rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [{\"role\": \"user\", \"content\": \"Hello!\"}],\\n    \"stream\": false\\n  }\\''\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 698,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                        children: \"Method 2: Bearer Token\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 710,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                                        title: \"Using Authorization Bearer header\",\n                                                                        language: \"bash\",\n                                                                        children: 'curl -X POST \"https://roukey.online/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"Authorization: Bearer rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [{\"role\": \"user\", \"content\": \"Hello!\"}],\\n    \"stream\": false\\n  }\\''\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 711,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                type: \"tip\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best Practice:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" Always use the \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-green-100 px-2 py-1 rounded text-green-800\",\n                                                        children: \"X-API-Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 68\n                                                    }, this),\n                                                    \" header method as it's the primary authentication method for RouKey and ensures maximum compatibility.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 724,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'api-reference' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"API Reference\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"Complete reference for the RouKey API endpoints and parameters.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Base URL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Production Base URL\",\n                                                        children: \"https://roukey.online/api/external/v1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Chat Completions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-6 text-lg\",\n                                                        children: \"Create a chat completion using RouKey's intelligent routing. This endpoint is fully compatible with OpenAI's API.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl mb-6 border border-green-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-green-600 text-white px-3 py-1 rounded-lg text-sm font-medium\",\n                                                                        children: \"POST\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 755,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                        className: \"text-gray-900 text-lg font-mono\",\n                                                                        children: \"/chat/completions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 756,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"OpenAI-compatible endpoint with RouKey's intelligent routing capabilities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 758,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                        children: \"Request Parameters\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-x-auto mb-8\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                            className: \"min-w-full bg-white border border-gray-200 rounded-xl shadow-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                    className: \"bg-gray-50\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-6 py-4 text-left text-sm font-medium text-gray-900\",\n                                                                                children: \"Parameter\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 768,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-6 py-4 text-left text-sm font-medium text-gray-900\",\n                                                                                children: \"Type\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 769,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-6 py-4 text-left text-sm font-medium text-gray-900\",\n                                                                                children: \"Required\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 770,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-6 py-4 text-left text-sm font-medium text-gray-900\",\n                                                                                children: \"Description\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 771,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 767,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                    className: \"divide-y divide-gray-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-[#ff6b35]\",\n                                                                                    children: \"messages\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 776,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"array\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 777,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-green-600\",\n                                                                                    children: \"Yes\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 778,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"Array of message objects\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 779,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-[#ff6b35]\",\n                                                                                    children: \"stream\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 782,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"boolean\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 783,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                                                    children: \"No\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 784,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"Enable streaming responses (recommended for multi-role tasks)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 785,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 781,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-[#ff6b35]\",\n                                                                                    children: \"temperature\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 788,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"number\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 789,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                                                    children: \"No\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 790,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"Sampling temperature (0-2)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 791,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 787,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-[#ff6b35]\",\n                                                                                    children: \"max_tokens\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 794,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"integer\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 795,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                                                    children: \"No\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 796,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"Maximum tokens to generate\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 797,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 793,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-[#ff6b35]\",\n                                                                                    children: \"role\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 800,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"string\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 801,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                                                    children: \"No\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 802,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: 'RouKey-specific role for routing (e.g., \"coding\", \"writing\")'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 803,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 799,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 774,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 765,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 764,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                        children: \"Example Request\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 809,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic chat completion\",\n                                                        language: \"json\",\n                                                        children: '{\\n  \"messages\": [\\n    {\"role\": \"user\", \"content\": \"Explain quantum computing\"}\\n  ],\\n  \"stream\": false,\\n  \"temperature\": 0.7,\\n  \"max_tokens\": 500\\n}'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 810,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900 mb-4 mt-8\",\n                                                        children: \"Example with Role-Based Routing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Role-based routing request\",\n                                                        language: \"json\",\n                                                        children: '{\\n  \"messages\": [\\n    {\"role\": \"user\", \"content\": \"Write a Python function to sort a list\"}\\n  ],\\n  \"role\": \"coding\",\\n  \"stream\": true,\\n  \"max_tokens\": 1000\\n}'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                        type: \"tip\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Streaming Recommended:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 834,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" For complex tasks that may involve multiple roles or require significant processing, use \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                className: \"bg-green-100 px-2 py-1 rounded text-green-800\",\n                                                                children: \"stream: true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 835,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \" to avoid timeouts and get real-time responses.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 833,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'examples' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Examples\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 844,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"Practical examples to get you started with RouKey in different programming languages.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 845,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 843,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"JavaScript/Node.js\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 851,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic chat completion with fetch\",\n                                                        language: \"javascript\",\n                                                        children: \"const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {\\n  method: 'POST',\\n  headers: {\\n    'Content-Type': 'application/json',\\n    'X-API-Key': 'rk_live_your_api_key_here'\\n  },\\n  body: JSON.stringify({\\n    messages: [\\n      { role: 'user', content: 'Explain machine learning in simple terms' }\\n    ],\\n    stream: false,\\n    max_tokens: 500\\n  })\\n});\\n\\nconst data = await response.json();\\nconsole.log(data.choices[0].message.content);\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 852,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Streaming response example\",\n                                                        language: \"javascript\",\n                                                        children: \"const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {\\n  method: 'POST',\\n  headers: {\\n    'Content-Type': 'application/json',\\n    'X-API-Key': 'rk_live_your_api_key_here'\\n  },\\n  body: JSON.stringify({\\n    messages: [\\n      { role: 'user', content: 'Write a detailed explanation of quantum computing' }\\n    ],\\n    stream: true,\\n    max_tokens: 1000\\n  })\\n});\\n\\nconst reader = response.body.getReader();\\nconst decoder = new TextDecoder();\\n\\nwhile (true) {\\n  const { done, value } = await reader.read();\\n  if (done) break;\\n\\n  const chunk = decoder.decode(value);\\n  const lines = chunk.split('\\\\n');\\n\\n  for (const line of lines) {\\n    if (line.startsWith('data: ')) {\\n      const data = line.slice(6);\\n      if (data === '[DONE]') return;\\n\\n      try {\\n        const parsed = JSON.parse(data);\\n        const content = parsed.choices[0]?.delta?.content;\\n        if (content) {\\n          process.stdout.write(content);\\n        }\\n      } catch (e) {\\n        // Skip invalid JSON\\n      }\\n    }\\n  }\\n}\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 872,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 850,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Python\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 919,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic chat completion with requests\",\n                                                        language: \"python\",\n                                                        children: \"import requests\\nimport json\\n\\nresponse = requests.post(\\n    'https://roukey.online/api/external/v1/chat/completions',\\n    headers={\\n        'Content-Type': 'application/json',\\n        'X-API-Key': 'rk_live_your_api_key_here'\\n    },\\n    json={\\n        'messages': [\\n            {'role': 'user', 'content': 'Explain machine learning in simple terms'}\\n        ],\\n        'stream': False,\\n        'max_tokens': 500\\n    }\\n)\\n\\ndata = response.json()\\nprint(data['choices'][0]['message']['content'])\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Streaming response with requests\",\n                                                        language: \"python\",\n                                                        children: \"import requests\\nimport json\\n\\nresponse = requests.post(\\n    'https://roukey.online/api/external/v1/chat/completions',\\n    headers={\\n        'Content-Type': 'application/json',\\n        'X-API-Key': 'rk_live_your_api_key_here'\\n    },\\n    json={\\n        'messages': [\\n            {'role': 'user', 'content': 'Write a detailed explanation of quantum computing'}\\n        ],\\n        'stream': True,\\n        'max_tokens': 1000\\n    },\\n    stream=True\\n)\\n\\nfor line in response.iter_lines():\\n    if line:\\n        line = line.decode('utf-8')\\n        if line.startswith('data: '):\\n            data = line[6:]\\n            if data == '[DONE]':\\n                break\\n            try:\\n                parsed = json.loads(data)\\n                content = parsed['choices'][0]['delta'].get('content', '')\\n                if content:\\n                    print(content, end='', flush=True)\\n            except json.JSONDecodeError:\\n                continue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 943,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 918,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"cURL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 981,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic request with cURL\",\n                                                        language: \"bash\",\n                                                        children: 'curl -X POST \"https://roukey.online/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"X-API-Key: rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [\\n      {\"role\": \"user\", \"content\": \"Hello, how are you?\"}\\n    ],\\n    \"stream\": false,\\n    \"max_tokens\": 150\\n  }\\''\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 982,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Role-based routing with cURL\",\n                                                        language: \"bash\",\n                                                        children: 'curl -X POST \"https://roukey.online/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"X-API-Key: rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [\\n      {\"role\": \"user\", \"content\": \"Write a Python function to calculate fibonacci numbers\"}\\n    ],\\n    \"role\": \"coding\",\\n    \"stream\": true,\\n    \"max_tokens\": 500\\n  }\\''\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 995,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                type: \"info\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Need more examples?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1011,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" Check out our GitHub repository for complete example applications and integration guides for popular frameworks like React, Vue, and Express.js.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 1010,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 842,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'routing-strategies' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Routing Strategies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1021,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"RouKey offers multiple intelligent routing strategies to optimize your LLM usage.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1022,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 1020,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                        children: \"Coming Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1027,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Detailed routing strategies documentation is being prepared.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1028,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 1026,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 1019,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'configuration' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1036,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"Learn how to configure RouKey for optimal performance.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1037,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 1035,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                        children: \"Coming Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Configuration documentation is being prepared.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1043,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 1041,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 1034,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'sdks' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"SDKs & Libraries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1051,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"Official SDKs and community libraries for RouKey.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1052,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 1050,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                        children: \"Coming Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1057,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"SDK documentation is being prepared.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1058,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 1056,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 1049,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'limits' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Limits & Pricing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1066,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"Understanding RouKey's usage limits and pricing structure.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1067,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 1065,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                        children: \"Coming Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1072,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Limits and pricing documentation is being prepared.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 1073,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 1071,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 1064,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, activeSection, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, this);\n}\n_s1(DocsPage, \"WaMce6WXmU7h4j2BVHbn60BQv2I=\");\n_c2 = DocsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CodeBlock\");\n$RefreshReg$(_c1, \"Alert\");\n$RefreshReg$(_c2, \"DocsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/docs/page.tsx\n"));

/***/ })

});