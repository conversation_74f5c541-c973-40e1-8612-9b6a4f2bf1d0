"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/docs/page",{

/***/ "(app-pages-browser)/./src/app/docs/page.tsx":
/*!*******************************!*\
  !*** ./src/app/docs/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nfunction CodeBlock(param) {\n    let { children, language = 'javascript', title } = param;\n    _s();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCopy = async ()=>{\n        await navigator.clipboard.writeText(children);\n        setCopied(true);\n        setTimeout(()=>setCopied(false), 2000);\n    };\n    // Enhanced syntax highlighting for different languages\n    const highlightSyntax = (code, lang)=>{\n        const lines = code.split('\\n');\n        return lines.map((line, index)=>{\n            let highlightedLine = line;\n            if (lang === 'javascript' || lang === 'typescript') {\n                // Keywords\n                highlightedLine = highlightedLine.replace(/\\b(const|let|var|function|async|await|import|export|from|return|if|else|for|while|try|catch|throw|new)\\b/g, '<span class=\"text-purple-400\">$1</span>');\n                // Strings\n                highlightedLine = highlightedLine.replace(/([\"'`])((?:\\\\.|(?!\\1)[^\\\\])*?)\\1/g, '<span class=\"text-green-400\">$1$2$1</span>');\n                // Comments\n                highlightedLine = highlightedLine.replace(/(\\/\\/.*$)/g, '<span class=\"text-gray-500\">$1</span>');\n                // Numbers\n                highlightedLine = highlightedLine.replace(/\\b(\\d+\\.?\\d*)\\b/g, '<span class=\"text-yellow-400\">$1</span>');\n            } else if (lang === 'python') {\n                // Python keywords\n                highlightedLine = highlightedLine.replace(/\\b(import|from|def|class|if|else|elif|for|while|try|except|with|as|return|yield|lambda|and|or|not|in|is|None|True|False)\\b/g, '<span class=\"text-purple-400\">$1</span>');\n                // Strings\n                highlightedLine = highlightedLine.replace(/([\"'`])((?:\\\\.|(?!\\1)[^\\\\])*?)\\1/g, '<span class=\"text-green-400\">$1$2$1</span>');\n                // Comments\n                highlightedLine = highlightedLine.replace(/(#.*$)/g, '<span class=\"text-gray-500\">$1</span>');\n            } else if (lang === 'bash' || lang === 'shell') {\n                // Bash commands\n                highlightedLine = highlightedLine.replace(/\\b(curl|echo|export|cd|ls|mkdir|rm|cp|mv|grep|awk|sed)\\b/g, '<span class=\"text-blue-400\">$1</span>');\n                // Flags\n                highlightedLine = highlightedLine.replace(/(-[a-zA-Z]+)/g, '<span class=\"text-yellow-400\">$1</span>');\n                // Strings\n                highlightedLine = highlightedLine.replace(/([\"'`])((?:\\\\.|(?!\\1)[^\\\\])*?)\\1/g, '<span class=\"text-green-400\">$1$2$1</span>');\n            } else if (lang === 'json') {\n                // JSON keys\n                highlightedLine = highlightedLine.replace(/\"([^\"]+)\":/g, '<span class=\"text-blue-400\">\"$1\"</span>:');\n                // JSON strings\n                highlightedLine = highlightedLine.replace(/:\\s*\"([^\"]*)\"/g, ': <span class=\"text-green-400\">\"$1\"</span>');\n                // JSON numbers\n                highlightedLine = highlightedLine.replace(/:\\s*(\\d+\\.?\\d*)/g, ': <span class=\"text-yellow-400\">$1</span>');\n                // JSON booleans\n                highlightedLine = highlightedLine.replace(/:\\s*(true|false|null)/g, ': <span class=\"text-purple-400\">$1</span>');\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"table-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"table-cell text-gray-500 text-right pr-4 select-none w-8\",\n                        children: index + 1\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"table-cell text-gray-100\",\n                        dangerouslySetInnerHTML: {\n                            __html: highlightedLine || ' '\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this);\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative bg-gray-900 rounded-xl overflow-hidden border border-gray-300 shadow-lg\",\n        children: [\n            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 px-4 py-3 text-sm text-gray-700 border-b border-gray-300 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-500 uppercase tracking-wide\",\n                        children: language\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: \"p-4 overflow-x-auto text-sm font-mono leading-relaxed\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"table w-full\",\n                            children: highlightSyntax(children, language || 'javascript')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCopy,\n                        className: \"absolute top-3 right-3 p-2 bg-white/90 hover:bg-white rounded-lg transition-all duration-200 border border-gray-300 shadow-sm\",\n                        title: \"Copy to clipboard\",\n                        children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-4 w-4 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-4 w-4 text-gray-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n_s(CodeBlock, \"NE86rL3vg4NVcTTWDavsT0hUBJs=\");\n_c = CodeBlock;\nfunction Alert(param) {\n    let { type, children } = param;\n    const styles = {\n        info: 'bg-blue-50 border-blue-200 text-blue-800',\n        warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n        tip: 'bg-green-50 border-green-200 text-green-800'\n    };\n    const iconStyles = {\n        info: 'text-blue-600',\n        warning: 'text-yellow-600',\n        tip: 'text-green-600'\n    };\n    const icons = {\n        info: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        warning: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        tip: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    };\n    const Icon = icons[type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border rounded-xl p-4 \".concat(styles[type]),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-5 w-5 flex-shrink-0 mt-0.5 \".concat(iconStyles[type])\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n            lineNumber: 193,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Alert;\nfunction DocsPage() {\n    _s1();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('getting-started');\n    const sections = [\n        {\n            id: 'getting-started',\n            title: 'Getting Started',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: 'authentication',\n            title: 'Authentication',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: 'api-reference',\n            title: 'API Reference',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: 'routing-strategies',\n            title: 'Routing Strategies',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            id: 'configuration',\n            title: 'Configuration',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            id: 'examples',\n            title: 'Examples',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: 'sdks',\n            title: 'SDKs & Libraries',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            id: 'limits',\n            title: 'Limits & Pricing',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80 flex-shrink-0 bg-white border-r border-gray-200 min-h-screen shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky top-0 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                            children: \"RouKey Documentation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"Complete guide to integrating and using RouKey's intelligent AI gateway\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"space-y-2\",\n                                    children: sections.map((section)=>{\n                                        const Icon = section.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveSection(section.id),\n                                            className: \"w-full flex items-center gap-3 px-4 py-3 text-sm rounded-xl transition-all duration-200 \".concat(activeSection === section.id ? 'bg-[#ff6b35] text-white shadow-lg shadow-orange-500/25' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 21\n                                                }, this),\n                                                section.title\n                                            ]\n                                        }, section.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 p-4 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 text-[#ff6b35]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: \"Quick Start\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600 mb-3\",\n                                            children: \"Get up and running in under 2 minutes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveSection('getting-started'),\n                                            className: \"w-full bg-[#ff6b35] hover:bg-[#e55a2b] text-white text-xs py-2 px-3 rounded-lg transition-colors\",\n                                            children: \"Start Building\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 bg-white text-gray-900 overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: [\n                                    activeSection === 'getting-started' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Getting Started with RouKey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"RouKey is an intelligent AI gateway that optimizes your LLM API usage through advanced routing strategies. Get up and running in under 2 minutes.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 p-6 rounded-xl border border-[#ff6b35]/20 hover:shadow-lg hover:border-[#ff6b35]/30 transition-all duration-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-[#ff6b35]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Quick Start\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-4\",\n                                                                children: \"Get up and running with RouKey in minutes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setActiveSection('authentication'),\n                                                                className: \"text-[#ff6b35] hover:text-[#e55a2b] font-medium flex items-center gap-1 transition-colors\",\n                                                                children: [\n                                                                    \"Start here \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 36\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-blue-50 to-blue-100/50 p-6 rounded-xl border border-blue-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"API Reference\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-4\",\n                                                                children: \"Complete API documentation and examples\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setActiveSection('api-reference'),\n                                                                className: \"text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1 transition-colors\",\n                                                                children: [\n                                                                    \"View API docs \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 39\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"What is RouKey?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"prose prose-gray max-w-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 leading-relaxed text-lg\",\n                                                            children: \"RouKey is an intelligent AI gateway that sits between your application and multiple LLM providers. It automatically routes requests to the most appropriate model based on your configured strategies, providing cost optimization, improved reliability, and enhanced performance.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Key Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4 p-6 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-[#ff6b35] flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 text-lg mb-2\",\n                                                                                children: \"Intelligent Routing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 336,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"AI-powered request classification and optimal model selection\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 337,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4 p-6 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-blue-600 flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 text-lg mb-2\",\n                                                                                children: \"Multiple Strategies\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 343,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Fallback, cost-optimized, role-based, and complexity routing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 344,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4 p-6 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-green-600 flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 text-lg mb-2\",\n                                                                                children: \"Secure & Reliable\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 350,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Enterprise-grade security with automatic failover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 351,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4 p-6 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-purple-600 flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 355,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 text-lg mb-2\",\n                                                                                children: \"Cost Optimization\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 357,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Reduce costs by up to 60% with smart routing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 358,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-8 rounded-xl text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold mb-4\",\n                                                        children: \"Ready to get started?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/90 mb-6\",\n                                                        children: \"Follow our quick start guide to integrate RouKey into your application in minutes.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveSection('authentication'),\n                                                        className: \"bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors\",\n                                                        children: \"Get Started Now\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'authentication' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Authentication\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"Learn how to authenticate with RouKey using API keys.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                type: \"info\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Important:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" RouKey uses the \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-blue-100 px-2 py-1 rounded text-blue-800\",\n                                                        children: \"X-API-Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    \" header for authentication. Never use \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-blue-100 px-2 py-1 rounded text-blue-800\",\n                                                        children: \"Authorization\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 31\n                                                    }, this),\n                                                    \" header format.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Getting Your API Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-lg\",\n                                                                children: \"To get started with RouKey, you'll need to create an API key from your dashboard:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                className: \"list-decimal list-inside space-y-3 text-gray-600 ml-4 text-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            \"Sign up for a RouKey account at \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                href: \"https://roukey.online\",\n                                                                                className: \"text-[#ff6b35] hover:text-[#e55a2b] underline\",\n                                                                                children: \"roukey.online\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 400,\n                                                                                columnNumber: 61\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 400,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Navigate to your dashboard and create a configuration\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Add your LLM provider API keys (OpenAI, Anthropic, etc.)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Generate a user API key for external access\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            \"Copy your API key (format: \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                className: \"bg-gray-100 px-2 py-1 rounded text-gray-700\",\n                                                                                children: \"rk_live_...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 404,\n                                                                                columnNumber: 56\n                                                                            }, this),\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Authentication Methods\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                        children: \"Method 1: X-API-Key Header (Recommended)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                                        title: \"Using X-API-Key header\",\n                                                                        language: \"bash\",\n                                                                        children: 'curl -X POST \"https://roukey.online/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"X-API-Key: rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [{\"role\": \"user\", \"content\": \"Hello!\"}],\\n    \"stream\": false\\n  }\\''\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                        children: \"Method 2: Bearer Token\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                                        title: \"Using Authorization Bearer header\",\n                                                                        language: \"bash\",\n                                                                        children: 'curl -X POST \"https://roukey.online/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"Authorization: Bearer rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [{\"role\": \"user\", \"content\": \"Hello!\"}],\\n    \"stream\": false\\n  }\\''\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                type: \"tip\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best Practice:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" Always use the \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-green-100 px-2 py-1 rounded text-green-800\",\n                                                        children: \"X-API-Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 68\n                                                    }, this),\n                                                    \" header method as it's the primary authentication method for RouKey and ensures maximum compatibility.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'api-reference' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"API Reference\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"Complete reference for the RouKey API endpoints and parameters.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Base URL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Production Base URL\",\n                                                        children: \"https://roukey.online/api/external/v1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Chat Completions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-6 text-lg\",\n                                                        children: \"Create a chat completion using RouKey's intelligent routing. This endpoint is fully compatible with OpenAI's API.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl mb-6 border border-green-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-green-600 text-white px-3 py-1 rounded-lg text-sm font-medium\",\n                                                                        children: \"POST\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                        className: \"text-gray-900 text-lg font-mono\",\n                                                                        children: \"/chat/completions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"OpenAI-compatible endpoint with RouKey's intelligent routing capabilities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                        children: \"Request Parameters\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-x-auto mb-8\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                            className: \"min-w-full bg-gray-800/50 border border-gray-700 rounded-xl\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                    className: \"bg-gray-700/50\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-6 py-4 text-left text-sm font-medium text-white\",\n                                                                                children: \"Parameter\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 484,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-6 py-4 text-left text-sm font-medium text-white\",\n                                                                                children: \"Type\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 485,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-6 py-4 text-left text-sm font-medium text-white\",\n                                                                                children: \"Required\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 486,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-6 py-4 text-left text-sm font-medium text-white\",\n                                                                                children: \"Description\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 487,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 483,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                    className: \"divide-y divide-gray-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-orange-400\",\n                                                                                    children: \"messages\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 492,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"array\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 493,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-green-400\",\n                                                                                    children: \"Yes\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 494,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"Array of message objects\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 495,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-orange-400\",\n                                                                                    children: \"stream\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 498,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"boolean\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 499,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                                                    children: \"No\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 500,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"Enable streaming responses (recommended for multi-role tasks)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 501,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 497,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-orange-400\",\n                                                                                    children: \"temperature\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 504,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"number\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 505,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                                                    children: \"No\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 506,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"Sampling temperature (0-2)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 507,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 503,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-orange-400\",\n                                                                                    children: \"max_tokens\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 510,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"integer\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 511,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                                                    children: \"No\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 512,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"Maximum tokens to generate\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 513,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 509,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-orange-400\",\n                                                                                    children: \"role\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 516,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"string\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 517,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                                                    children: \"No\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 518,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: 'RouKey-specific role for routing (e.g., \"coding\", \"writing\")'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 519,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 515,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                        children: \"Example Request\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic chat completion\",\n                                                        language: \"json\",\n                                                        children: '{\\n  \"messages\": [\\n    {\"role\": \"user\", \"content\": \"Explain quantum computing\"}\\n  ],\\n  \"stream\": false,\\n  \"temperature\": 0.7,\\n  \"max_tokens\": 500\\n}'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-white mb-4 mt-8\",\n                                                        children: \"Example with Role-Based Routing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Role-based routing request\",\n                                                        language: \"json\",\n                                                        children: '{\\n  \"messages\": [\\n    {\"role\": \"user\", \"content\": \"Write a Python function to sort a list\"}\\n  ],\\n  \"role\": \"coding\",\\n  \"stream\": true,\\n  \"max_tokens\": 1000\\n}'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                        type: \"tip\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Streaming Recommended:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" For complex tasks that may involve multiple roles or require significant processing, use \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                className: \"bg-green-900/50 px-2 py-1 rounded text-green-300\",\n                                                                children: \"stream: true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \" to avoid timeouts and get real-time responses.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'examples' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-white mb-6\",\n                                                        children: \"Examples\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                                        children: \"Practical examples to get you started with RouKey in different programming languages.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white mb-6\",\n                                                        children: \"JavaScript/Node.js\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic chat completion with fetch\",\n                                                        language: \"javascript\",\n                                                        children: \"const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {\\n  method: 'POST',\\n  headers: {\\n    'Content-Type': 'application/json',\\n    'X-API-Key': 'rk_live_your_api_key_here'\\n  },\\n  body: JSON.stringify({\\n    messages: [\\n      { role: 'user', content: 'Explain machine learning in simple terms' }\\n    ],\\n    stream: false,\\n    max_tokens: 500\\n  })\\n});\\n\\nconst data = await response.json();\\nconsole.log(data.choices[0].message.content);\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Streaming response example\",\n                                                        language: \"javascript\",\n                                                        children: \"const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {\\n  method: 'POST',\\n  headers: {\\n    'Content-Type': 'application/json',\\n    'X-API-Key': 'rk_live_your_api_key_here'\\n  },\\n  body: JSON.stringify({\\n    messages: [\\n      { role: 'user', content: 'Write a detailed explanation of quantum computing' }\\n    ],\\n    stream: true,\\n    max_tokens: 1000\\n  })\\n});\\n\\nconst reader = response.body.getReader();\\nconst decoder = new TextDecoder();\\n\\nwhile (true) {\\n  const { done, value } = await reader.read();\\n  if (done) break;\\n\\n  const chunk = decoder.decode(value);\\n  const lines = chunk.split('\\\\n');\\n\\n  for (const line of lines) {\\n    if (line.startsWith('data: ')) {\\n      const data = line.slice(6);\\n      if (data === '[DONE]') return;\\n\\n      try {\\n        const parsed = JSON.parse(data);\\n        const content = parsed.choices[0]?.delta?.content;\\n        if (content) {\\n          process.stdout.write(content);\\n        }\\n      } catch (e) {\\n        // Skip invalid JSON\\n      }\\n    }\\n  }\\n}\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white mb-6\",\n                                                        children: \"Python\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic chat completion with requests\",\n                                                        language: \"python\",\n                                                        children: \"import requests\\nimport json\\n\\nresponse = requests.post(\\n    'https://roukey.online/api/external/v1/chat/completions',\\n    headers={\\n        'Content-Type': 'application/json',\\n        'X-API-Key': 'rk_live_your_api_key_here'\\n    },\\n    json={\\n        'messages': [\\n            {'role': 'user', 'content': 'Explain machine learning in simple terms'}\\n        ],\\n        'stream': False,\\n        'max_tokens': 500\\n    }\\n)\\n\\ndata = response.json()\\nprint(data['choices'][0]['message']['content'])\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Streaming response with requests\",\n                                                        language: \"python\",\n                                                        children: \"import requests\\nimport json\\n\\nresponse = requests.post(\\n    'https://roukey.online/api/external/v1/chat/completions',\\n    headers={\\n        'Content-Type': 'application/json',\\n        'X-API-Key': 'rk_live_your_api_key_here'\\n    },\\n    json={\\n        'messages': [\\n            {'role': 'user', 'content': 'Write a detailed explanation of quantum computing'}\\n        ],\\n        'stream': True,\\n        'max_tokens': 1000\\n    },\\n    stream=True\\n)\\n\\nfor line in response.iter_lines():\\n    if line:\\n        line = line.decode('utf-8')\\n        if line.startswith('data: '):\\n            data = line[6:]\\n            if data == '[DONE]':\\n                break\\n            try:\\n                parsed = json.loads(data)\\n                content = parsed['choices'][0]['delta'].get('content', '')\\n                if content:\\n                    print(content, end='', flush=True)\\n            except json.JSONDecodeError:\\n                continue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white mb-6\",\n                                                        children: \"cURL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic request with cURL\",\n                                                        language: \"bash\",\n                                                        children: 'curl -X POST \"https://roukey.online/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"X-API-Key: rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [\\n      {\"role\": \"user\", \"content\": \"Hello, how are you?\"}\\n    ],\\n    \"stream\": false,\\n    \"max_tokens\": 150\\n  }\\''\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Role-based routing with cURL\",\n                                                        language: \"bash\",\n                                                        children: 'curl -X POST \"https://roukey.online/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"X-API-Key: rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [\\n      {\"role\": \"user\", \"content\": \"Write a Python function to calculate fibonacci numbers\"}\\n    ],\\n    \"role\": \"coding\",\\n    \"stream\": true,\\n    \"max_tokens\": 500\\n  }\\''\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                type: \"info\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Need more examples?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" Check out our GitHub repository for complete example applications and integration guides for popular frameworks like React, Vue, and Express.js.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 726,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'routing-strategies' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-white mb-6\",\n                                                        children: \"Routing Strategies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                                        children: \"RouKey offers multiple intelligent routing strategies to optimize your LLM usage.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                        children: \"Coming Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"Detailed routing strategies documentation is being prepared.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 742,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'configuration' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-white mb-6\",\n                                                        children: \"Configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                                        children: \"Learn how to configure RouKey for optimal performance.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                        children: \"Coming Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 758,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"Configuration documentation is being prepared.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 757,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'sdks' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-white mb-6\",\n                                                        children: \"SDKs & Libraries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                                        children: \"Official SDKs and community libraries for RouKey.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                        children: \"Coming Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"SDK documentation is being prepared.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'limits' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-white mb-6\",\n                                                        children: \"Limits & Pricing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                                        children: \"Understanding RouKey's usage limits and pricing structure.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                        children: \"Coming Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"Limits and pricing documentation is being prepared.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 787,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 780,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, activeSection, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, this);\n}\n_s1(DocsPage, \"x6z6yFCb1AOn1CFzZeI0YdLu3rM=\");\n_c2 = DocsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CodeBlock\");\n$RefreshReg$(_c1, \"Alert\");\n$RefreshReg$(_c2, \"DocsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/docs/page.tsx\n"));

/***/ })

});