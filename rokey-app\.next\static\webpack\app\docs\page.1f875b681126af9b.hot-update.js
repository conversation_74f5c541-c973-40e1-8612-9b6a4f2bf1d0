"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/docs/page",{

/***/ "(app-pages-browser)/./src/app/docs/page.tsx":
/*!*******************************!*\
  !*** ./src/app/docs/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nfunction CodeBlock(param) {\n    let { children, language = 'javascript', title } = param;\n    _s();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCopy = async ()=>{\n        await navigator.clipboard.writeText(children);\n        setCopied(true);\n        setTimeout(()=>setCopied(false), 2000);\n    };\n    // Enhanced syntax highlighting for different languages\n    const highlightSyntax = (code, lang)=>{\n        const lines = code.split('\\n');\n        return lines.map((line, index)=>{\n            let highlightedLine = line;\n            if (lang === 'javascript' || lang === 'typescript') {\n                // Keywords\n                highlightedLine = highlightedLine.replace(/\\b(const|let|var|function|async|await|import|export|from|return|if|else|for|while|try|catch|throw|new)\\b/g, '<span class=\"text-purple-400\">$1</span>');\n                // Strings\n                highlightedLine = highlightedLine.replace(/([\"'`])((?:\\\\.|(?!\\1)[^\\\\])*?)\\1/g, '<span class=\"text-green-400\">$1$2$1</span>');\n                // Comments\n                highlightedLine = highlightedLine.replace(/(\\/\\/.*$)/g, '<span class=\"text-gray-500\">$1</span>');\n                // Numbers\n                highlightedLine = highlightedLine.replace(/\\b(\\d+\\.?\\d*)\\b/g, '<span class=\"text-yellow-400\">$1</span>');\n            } else if (lang === 'python') {\n                // Python keywords\n                highlightedLine = highlightedLine.replace(/\\b(import|from|def|class|if|else|elif|for|while|try|except|with|as|return|yield|lambda|and|or|not|in|is|None|True|False)\\b/g, '<span class=\"text-purple-400\">$1</span>');\n                // Strings\n                highlightedLine = highlightedLine.replace(/([\"'`])((?:\\\\.|(?!\\1)[^\\\\])*?)\\1/g, '<span class=\"text-green-400\">$1$2$1</span>');\n                // Comments\n                highlightedLine = highlightedLine.replace(/(#.*$)/g, '<span class=\"text-gray-500\">$1</span>');\n            } else if (lang === 'bash' || lang === 'shell') {\n                // Bash commands\n                highlightedLine = highlightedLine.replace(/\\b(curl|echo|export|cd|ls|mkdir|rm|cp|mv|grep|awk|sed)\\b/g, '<span class=\"text-blue-400\">$1</span>');\n                // Flags\n                highlightedLine = highlightedLine.replace(/(-[a-zA-Z]+)/g, '<span class=\"text-yellow-400\">$1</span>');\n                // Strings\n                highlightedLine = highlightedLine.replace(/([\"'`])((?:\\\\.|(?!\\1)[^\\\\])*?)\\1/g, '<span class=\"text-green-400\">$1$2$1</span>');\n            } else if (lang === 'json') {\n                // JSON keys\n                highlightedLine = highlightedLine.replace(/\"([^\"]+)\":/g, '<span class=\"text-blue-400\">\"$1\"</span>:');\n                // JSON strings\n                highlightedLine = highlightedLine.replace(/:\\s*\"([^\"]*)\"/g, ': <span class=\"text-green-400\">\"$1\"</span>');\n                // JSON numbers\n                highlightedLine = highlightedLine.replace(/:\\s*(\\d+\\.?\\d*)/g, ': <span class=\"text-yellow-400\">$1</span>');\n                // JSON booleans\n                highlightedLine = highlightedLine.replace(/:\\s*(true|false|null)/g, ': <span class=\"text-purple-400\">$1</span>');\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"table-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"table-cell text-gray-500 text-right pr-4 select-none w-8\",\n                        children: index + 1\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"table-cell text-gray-100\",\n                        dangerouslySetInnerHTML: {\n                            __html: highlightedLine || ' '\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this);\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative bg-gray-900 rounded-xl overflow-hidden border border-gray-300 shadow-lg\",\n        children: [\n            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 px-4 py-3 text-sm text-gray-700 border-b border-gray-300 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-500 uppercase tracking-wide\",\n                        children: language\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: \"p-4 overflow-x-auto text-sm font-mono leading-relaxed\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"table w-full\",\n                            children: highlightSyntax(children, language || 'javascript')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCopy,\n                        className: \"absolute top-3 right-3 p-2 bg-white/90 hover:bg-white rounded-lg transition-all duration-200 border border-gray-300 shadow-sm\",\n                        title: \"Copy to clipboard\",\n                        children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-4 w-4 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-4 w-4 text-gray-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n_s(CodeBlock, \"NE86rL3vg4NVcTTWDavsT0hUBJs=\");\n_c = CodeBlock;\nfunction Alert(param) {\n    let { type, children } = param;\n    const styles = {\n        info: 'bg-blue-50 border-blue-200 text-blue-800',\n        warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n        tip: 'bg-green-50 border-green-200 text-green-800'\n    };\n    const iconStyles = {\n        info: 'text-blue-600',\n        warning: 'text-yellow-600',\n        tip: 'text-green-600'\n    };\n    const icons = {\n        info: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        warning: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        tip: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    };\n    const Icon = icons[type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border rounded-xl p-4 \".concat(styles[type]),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-5 w-5 flex-shrink-0 mt-0.5 \".concat(iconStyles[type])\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n            lineNumber: 193,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Alert;\nfunction DocsPage() {\n    _s1();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('getting-started');\n    const sections = [\n        {\n            id: 'getting-started',\n            title: 'Getting Started',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: 'authentication',\n            title: 'Authentication',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: 'api-reference',\n            title: 'API Reference',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: 'routing-strategies',\n            title: 'Routing Strategies',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            id: 'configuration',\n            title: 'Configuration',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            id: 'examples',\n            title: 'Examples',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: 'sdks',\n            title: 'SDKs & Libraries',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            id: 'limits',\n            title: 'Limits & Pricing',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-16 flex flex-col lg:flex-row min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full lg:w-80 flex-shrink-0 bg-white border-r border-gray-200 shadow-sm lg:fixed lg:top-16 lg:left-0 lg:h-[calc(100vh-4rem)] lg:z-10 lg:overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 lg:p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                            children: \"RouKey Documentation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"Complete guide to integrating and using RouKey's intelligent AI gateway\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"space-y-2\",\n                                    children: sections.map((section)=>{\n                                        const Icon = section.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveSection(section.id),\n                                            className: \"w-full flex items-center gap-3 px-4 py-3 text-sm rounded-xl transition-all duration-200 \".concat(activeSection === section.id ? 'bg-[#ff6b35] text-white shadow-lg shadow-orange-500/25' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 21\n                                                }, this),\n                                                section.title\n                                            ]\n                                        }, section.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 p-4 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 text-[#ff6b35]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: \"Quick Start\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600 mb-3\",\n                                            children: \"Get up and running in under 2 minutes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveSection('getting-started'),\n                                            className: \"w-full bg-[#ff6b35] hover:bg-[#e55a2b] text-white text-xs py-2 px-3 rounded-lg transition-colors\",\n                                            children: \"Start Building\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 bg-white text-gray-900 min-h-screen lg:ml-80\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto p-4 lg:p-8 lg:py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: [\n                                    activeSection === 'getting-started' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Getting Started with RouKey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"RouKey is an intelligent AI gateway that optimizes your LLM API usage through advanced routing strategies. Get up and running in under 2 minutes.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 p-6 rounded-xl border border-[#ff6b35]/20 hover:shadow-lg hover:border-[#ff6b35]/30 transition-all duration-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-[#ff6b35]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Quick Start\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-4\",\n                                                                children: \"Get up and running with RouKey in minutes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setActiveSection('authentication'),\n                                                                className: \"text-[#ff6b35] hover:text-[#e55a2b] font-medium flex items-center gap-1 transition-colors\",\n                                                                children: [\n                                                                    \"Start here \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 36\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-blue-50 to-blue-100/50 p-6 rounded-xl border border-blue-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"API Reference\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-4\",\n                                                                children: \"Complete API documentation and examples\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setActiveSection('api-reference'),\n                                                                className: \"text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1 transition-colors\",\n                                                                children: [\n                                                                    \"View API docs \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 315,\n                                                                        columnNumber: 39\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"What is RouKey?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"prose prose-gray max-w-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 leading-relaxed text-lg\",\n                                                            children: \"RouKey is an intelligent AI gateway that sits between your application and multiple LLM providers. It automatically routes requests to the most appropriate model based on your configured strategies, providing cost optimization, improved reliability, and enhanced performance.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Key Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4 p-6 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-[#ff6b35] flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 text-lg mb-2\",\n                                                                                children: \"Intelligent Routing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 337,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"AI-powered request classification and optimal model selection\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 338,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4 p-6 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-blue-600 flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 text-lg mb-2\",\n                                                                                children: \"Multiple Strategies\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 344,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Fallback, cost-optimized, role-based, and complexity routing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 345,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4 p-6 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-green-600 flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 text-lg mb-2\",\n                                                                                children: \"Secure & Reliable\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 351,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Enterprise-grade security with automatic failover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4 p-6 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-purple-600 flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900 text-lg mb-2\",\n                                                                                children: \"Cost Optimization\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 358,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Reduce costs by up to 60% with smart routing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 359,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-8 rounded-xl text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold mb-4\",\n                                                        children: \"Ready to get started?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/90 mb-6\",\n                                                        children: \"Follow our quick start guide to integrate RouKey into your application in minutes.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveSection('authentication'),\n                                                        className: \"bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors\",\n                                                        children: \"Get Started Now\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'authentication' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Authentication\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"Learn how to authenticate with RouKey using API keys.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                type: \"info\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Important:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" RouKey uses the \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-blue-100 px-2 py-1 rounded text-blue-800\",\n                                                        children: \"X-API-Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    \" header for authentication. Never use \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-blue-100 px-2 py-1 rounded text-blue-800\",\n                                                        children: \"Authorization\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 31\n                                                    }, this),\n                                                    \" header format.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Getting Your API Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-lg\",\n                                                                children: \"To get started with RouKey, you'll need to create an API key from your dashboard:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                className: \"list-decimal list-inside space-y-3 text-gray-600 ml-4 text-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            \"Sign up for a RouKey account at \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                href: \"https://roukey.online\",\n                                                                                className: \"text-[#ff6b35] hover:text-[#e55a2b] underline\",\n                                                                                children: \"roukey.online\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 401,\n                                                                                columnNumber: 61\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Navigate to your dashboard and create a configuration\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Add your LLM provider API keys (OpenAI, Anthropic, etc.)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Generate a user API key for external access\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            \"Copy your API key (format: \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                className: \"bg-gray-100 px-2 py-1 rounded text-gray-700\",\n                                                                                children: \"rk_live_...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 405,\n                                                                                columnNumber: 56\n                                                                            }, this),\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 405,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Authentication Methods\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                        children: \"Method 1: X-API-Key Header (Recommended)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                                        title: \"Using X-API-Key header\",\n                                                                        language: \"bash\",\n                                                                        children: 'curl -X POST \"https://roukey.online/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"X-API-Key: rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [{\"role\": \"user\", \"content\": \"Hello!\"}],\\n    \"stream\": false\\n  }\\''\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                        children: \"Method 2: Bearer Token\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                                        title: \"Using Authorization Bearer header\",\n                                                                        language: \"bash\",\n                                                                        children: 'curl -X POST \"https://roukey.online/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"Authorization: Bearer rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [{\"role\": \"user\", \"content\": \"Hello!\"}],\\n    \"stream\": false\\n  }\\''\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                type: \"tip\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best Practice:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" Always use the \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-green-100 px-2 py-1 rounded text-green-800\",\n                                                        children: \"X-API-Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 68\n                                                    }, this),\n                                                    \" header method as it's the primary authentication method for RouKey and ensures maximum compatibility.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'api-reference' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"API Reference\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"Complete reference for the RouKey API endpoints and parameters.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Base URL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Production Base URL\",\n                                                        children: \"https://roukey.online/api/external/v1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Chat Completions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-6 text-lg\",\n                                                        children: \"Create a chat completion using RouKey's intelligent routing. This endpoint is fully compatible with OpenAI's API.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl mb-6 border border-green-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-green-600 text-white px-3 py-1 rounded-lg text-sm font-medium\",\n                                                                        children: \"POST\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                        className: \"text-gray-900 text-lg font-mono\",\n                                                                        children: \"/chat/completions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 473,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"OpenAI-compatible endpoint with RouKey's intelligent routing capabilities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                        children: \"Request Parameters\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-x-auto mb-8\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                            className: \"min-w-full bg-white border border-gray-200 rounded-xl shadow-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                    className: \"bg-gray-50\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-6 py-4 text-left text-sm font-medium text-gray-900\",\n                                                                                children: \"Parameter\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 485,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-6 py-4 text-left text-sm font-medium text-gray-900\",\n                                                                                children: \"Type\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 486,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-6 py-4 text-left text-sm font-medium text-gray-900\",\n                                                                                children: \"Required\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 487,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-6 py-4 text-left text-sm font-medium text-gray-900\",\n                                                                                children: \"Description\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 488,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 484,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                    className: \"divide-y divide-gray-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-[#ff6b35]\",\n                                                                                    children: \"messages\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 493,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"array\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 494,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-green-600\",\n                                                                                    children: \"Yes\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 495,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"Array of message objects\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 496,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 492,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-[#ff6b35]\",\n                                                                                    children: \"stream\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 499,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"boolean\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 500,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                                                    children: \"No\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 501,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"Enable streaming responses (recommended for multi-role tasks)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 502,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 498,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-[#ff6b35]\",\n                                                                                    children: \"temperature\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 505,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"number\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 506,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                                                    children: \"No\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 507,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"Sampling temperature (0-2)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 508,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 504,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-[#ff6b35]\",\n                                                                                    children: \"max_tokens\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 511,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"integer\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 512,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                                                    children: \"No\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 513,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"Maximum tokens to generate\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 514,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 510,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-[#ff6b35]\",\n                                                                                    children: \"role\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 517,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: \"string\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 518,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                                                    children: \"No\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 519,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-600\",\n                                                                                    children: 'RouKey-specific role for routing (e.g., \"coding\", \"writing\")'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 520,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 516,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                        children: \"Example Request\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic chat completion\",\n                                                        language: \"json\",\n                                                        children: '{\\n  \"messages\": [\\n    {\"role\": \"user\", \"content\": \"Explain quantum computing\"}\\n  ],\\n  \"stream\": false,\\n  \"temperature\": 0.7,\\n  \"max_tokens\": 500\\n}'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900 mb-4 mt-8\",\n                                                        children: \"Example with Role-Based Routing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Role-based routing request\",\n                                                        language: \"json\",\n                                                        children: '{\\n  \"messages\": [\\n    {\"role\": \"user\", \"content\": \"Write a Python function to sort a list\"}\\n  ],\\n  \"role\": \"coding\",\\n  \"stream\": true,\\n  \"max_tokens\": 1000\\n}'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                        type: \"tip\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Streaming Recommended:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" For complex tasks that may involve multiple roles or require significant processing, use \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                className: \"bg-green-100 px-2 py-1 rounded text-green-800\",\n                                                                children: \"stream: true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 552,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \" to avoid timeouts and get real-time responses.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'examples' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Examples\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"Practical examples to get you started with RouKey in different programming languages.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"JavaScript/Node.js\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic chat completion with fetch\",\n                                                        language: \"javascript\",\n                                                        children: \"const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {\\n  method: 'POST',\\n  headers: {\\n    'Content-Type': 'application/json',\\n    'X-API-Key': 'rk_live_your_api_key_here'\\n  },\\n  body: JSON.stringify({\\n    messages: [\\n      { role: 'user', content: 'Explain machine learning in simple terms' }\\n    ],\\n    stream: false,\\n    max_tokens: 500\\n  })\\n});\\n\\nconst data = await response.json();\\nconsole.log(data.choices[0].message.content);\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Streaming response example\",\n                                                        language: \"javascript\",\n                                                        children: \"const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {\\n  method: 'POST',\\n  headers: {\\n    'Content-Type': 'application/json',\\n    'X-API-Key': 'rk_live_your_api_key_here'\\n  },\\n  body: JSON.stringify({\\n    messages: [\\n      { role: 'user', content: 'Write a detailed explanation of quantum computing' }\\n    ],\\n    stream: true,\\n    max_tokens: 1000\\n  })\\n});\\n\\nconst reader = response.body.getReader();\\nconst decoder = new TextDecoder();\\n\\nwhile (true) {\\n  const { done, value } = await reader.read();\\n  if (done) break;\\n\\n  const chunk = decoder.decode(value);\\n  const lines = chunk.split('\\\\n');\\n\\n  for (const line of lines) {\\n    if (line.startsWith('data: ')) {\\n      const data = line.slice(6);\\n      if (data === '[DONE]') return;\\n\\n      try {\\n        const parsed = JSON.parse(data);\\n        const content = parsed.choices[0]?.delta?.content;\\n        if (content) {\\n          process.stdout.write(content);\\n        }\\n      } catch (e) {\\n        // Skip invalid JSON\\n      }\\n    }\\n  }\\n}\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Python\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic chat completion with requests\",\n                                                        language: \"python\",\n                                                        children: \"import requests\\nimport json\\n\\nresponse = requests.post(\\n    'https://roukey.online/api/external/v1/chat/completions',\\n    headers={\\n        'Content-Type': 'application/json',\\n        'X-API-Key': 'rk_live_your_api_key_here'\\n    },\\n    json={\\n        'messages': [\\n            {'role': 'user', 'content': 'Explain machine learning in simple terms'}\\n        ],\\n        'stream': False,\\n        'max_tokens': 500\\n    }\\n)\\n\\ndata = response.json()\\nprint(data['choices'][0]['message']['content'])\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Streaming response with requests\",\n                                                        language: \"python\",\n                                                        children: \"import requests\\nimport json\\n\\nresponse = requests.post(\\n    'https://roukey.online/api/external/v1/chat/completions',\\n    headers={\\n        'Content-Type': 'application/json',\\n        'X-API-Key': 'rk_live_your_api_key_here'\\n    },\\n    json={\\n        'messages': [\\n            {'role': 'user', 'content': 'Write a detailed explanation of quantum computing'}\\n        ],\\n        'stream': True,\\n        'max_tokens': 1000\\n    },\\n    stream=True\\n)\\n\\nfor line in response.iter_lines():\\n    if line:\\n        line = line.decode('utf-8')\\n        if line.startswith('data: '):\\n            data = line[6:]\\n            if data == '[DONE]':\\n                break\\n            try:\\n                parsed = json.loads(data)\\n                content = parsed['choices'][0]['delta'].get('content', '')\\n                if content:\\n                    print(content, end='', flush=True)\\n            except json.JSONDecodeError:\\n                continue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                                        children: \"cURL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic request with cURL\",\n                                                        language: \"bash\",\n                                                        children: 'curl -X POST \"https://roukey.online/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"X-API-Key: rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [\\n      {\"role\": \"user\", \"content\": \"Hello, how are you?\"}\\n    ],\\n    \"stream\": false,\\n    \"max_tokens\": 150\\n  }\\''\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Role-based routing with cURL\",\n                                                        language: \"bash\",\n                                                        children: 'curl -X POST \"https://roukey.online/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"X-API-Key: rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [\\n      {\"role\": \"user\", \"content\": \"Write a Python function to calculate fibonacci numbers\"}\\n    ],\\n    \"role\": \"coding\",\\n    \"stream\": true,\\n    \"max_tokens\": 500\\n  }\\''\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                type: \"info\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Need more examples?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" Check out our GitHub repository for complete example applications and integration guides for popular frameworks like React, Vue, and Express.js.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 727,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'routing-strategies' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Routing Strategies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"RouKey offers multiple intelligent routing strategies to optimize your LLM usage.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                        children: \"Coming Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Detailed routing strategies documentation is being prepared.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 745,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 743,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 736,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'configuration' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"Learn how to configure RouKey for optimal performance.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                        children: \"Coming Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Configuration documentation is being prepared.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'sdks' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"SDKs & Libraries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"Official SDKs and community libraries for RouKey.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 767,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                        children: \"Coming Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"SDK documentation is being prepared.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 773,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 766,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'limits' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                        children: \"Limits & Pricing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                                        children: \"Understanding RouKey's usage limits and pricing structure.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 784,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 782,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                        children: \"Coming Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Limits and pricing documentation is being prepared.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 790,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, activeSection, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, this);\n}\n_s1(DocsPage, \"x6z6yFCb1AOn1CFzZeI0YdLu3rM=\");\n_c2 = DocsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CodeBlock\");\n$RefreshReg$(_c1, \"Alert\");\n$RefreshReg$(_c2, \"DocsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/docs/page.tsx\n"));

/***/ })

});