"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/docs/page",{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ChevronRightIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n    }));\n}\n_c = ChevronRightIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ChevronRightIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChevronRightIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction DocumentTextIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n    }));\n}\n_c = DocumentTextIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(DocumentTextIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"DocumentTextIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js":
/*!************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ListBulletIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n    }));\n}\n_c = ListBulletIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ListBulletIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ListBulletIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL0xpc3RCdWxsZXRJY29uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStCO0FBQy9CLFNBQVNDLGVBQWUsS0FJdkIsRUFBRUMsTUFBTTtRQUplLEVBQ3RCQyxLQUFLLEVBQ0xDLE9BQU8sRUFDUCxHQUFHQyxPQUNKLEdBSnVCO0lBS3RCLE9BQU8sV0FBVyxHQUFFTCxnREFBbUIsQ0FBQyxPQUFPTyxPQUFPQyxNQUFNLENBQUM7UUFDM0RDLE9BQU87UUFDUEMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLGFBQWE7UUFDYkMsUUFBUTtRQUNSLGVBQWU7UUFDZixhQUFhO1FBQ2JDLEtBQUtaO1FBQ0wsbUJBQW1CRTtJQUNyQixHQUFHQyxRQUFRRixRQUFRLFdBQVcsR0FBRUgsZ0RBQW1CLENBQUMsU0FBUztRQUMzRGUsSUFBSVg7SUFDTixHQUFHRCxTQUFTLE1BQU0sV0FBVyxHQUFFSCxnREFBbUIsQ0FBQyxRQUFRO1FBQ3pEZ0IsZUFBZTtRQUNmQyxnQkFBZ0I7UUFDaEJDLEdBQUc7SUFDTDtBQUNGO0tBdEJTakI7QUF1QlQsTUFBTWtCLGFBQWEsV0FBVyxHQUFHbkIsNkNBQWdCLENBQUNDOztBQUNsRCxpRUFBZWtCLFVBQVVBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxAaGVyb2ljb25zXFxyZWFjdFxcMjRcXG91dGxpbmVcXGVzbVxcTGlzdEJ1bGxldEljb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiBMaXN0QnVsbGV0SWNvbih7XG4gIHRpdGxlLFxuICB0aXRsZUlkLFxuICAuLi5wcm9wc1xufSwgc3ZnUmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInN2Z1wiLCBPYmplY3QuYXNzaWduKHtcbiAgICB4bWxuczogXCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiLFxuICAgIGZpbGw6IFwibm9uZVwiLFxuICAgIHZpZXdCb3g6IFwiMCAwIDI0IDI0XCIsXG4gICAgc3Ryb2tlV2lkdGg6IDEuNSxcbiAgICBzdHJva2U6IFwiY3VycmVudENvbG9yXCIsXG4gICAgXCJhcmlhLWhpZGRlblwiOiBcInRydWVcIixcbiAgICBcImRhdGEtc2xvdFwiOiBcImljb25cIixcbiAgICByZWY6IHN2Z1JlZixcbiAgICBcImFyaWEtbGFiZWxsZWRieVwiOiB0aXRsZUlkXG4gIH0sIHByb3BzKSwgdGl0bGUgPyAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInRpdGxlXCIsIHtcbiAgICBpZDogdGl0bGVJZFxuICB9LCB0aXRsZSkgOiBudWxsLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInBhdGhcIiwge1xuICAgIHN0cm9rZUxpbmVjYXA6IFwicm91bmRcIixcbiAgICBzdHJva2VMaW5lam9pbjogXCJyb3VuZFwiLFxuICAgIGQ6IFwiTTguMjUgNi43NWgxMk04LjI1IDEyaDEybS0xMiA1LjI1aDEyTTMuNzUgNi43NWguMDA3di4wMDhIMy43NVY2Ljc1Wm0uMzc1IDBhLjM3NS4zNzUgMCAxIDEtLjc1IDAgLjM3NS4zNzUgMCAwIDEgLjc1IDBaTTMuNzUgMTJoLjAwN3YuMDA4SDMuNzVWMTJabS4zNzUgMGEuMzc1LjM3NSAwIDEgMS0uNzUgMCAuMzc1LjM3NSAwIDAgMSAuNzUgMFptLS4zNzUgNS4yNWguMDA3di4wMDhIMy43NXYtLjAwOFptLjM3NSAwYS4zNzUuMzc1IDAgMSAxLS43NSAwIC4zNzUuMzc1IDAgMCAxIC43NSAwWlwiXG4gIH0pKTtcbn1cbmNvbnN0IEZvcndhcmRSZWYgPSAvKiNfX1BVUkVfXyovIFJlYWN0LmZvcndhcmRSZWYoTGlzdEJ1bGxldEljb24pO1xuZXhwb3J0IGRlZmF1bHQgRm9yd2FyZFJlZjsiXSwibmFtZXMiOlsiUmVhY3QiLCJMaXN0QnVsbGV0SWNvbiIsInN2Z1JlZiIsInRpdGxlIiwidGl0bGVJZCIsInByb3BzIiwiY3JlYXRlRWxlbWVudCIsIk9iamVjdCIsImFzc2lnbiIsInhtbG5zIiwiZmlsbCIsInZpZXdCb3giLCJzdHJva2VXaWR0aCIsInN0cm9rZSIsInJlZiIsImlkIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwiZCIsIkZvcndhcmRSZWYiLCJmb3J3YXJkUmVmIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/docs/page.tsx":
/*!*******************************!*\
  !*** ./src/app/docs/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nfunction CodeBlock(param) {\n    let { children, language = 'javascript', title } = param;\n    _s();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCopy = async ()=>{\n        await navigator.clipboard.writeText(children);\n        setCopied(true);\n        setTimeout(()=>setCopied(false), 2000);\n    };\n    // Enhanced syntax highlighting for different languages\n    const highlightSyntax = (code, lang)=>{\n        const lines = code.split('\\n');\n        return lines.map((line, index)=>{\n            let highlightedLine = line;\n            if (lang === 'javascript' || lang === 'typescript') {\n                // Keywords\n                highlightedLine = highlightedLine.replace(/\\b(const|let|var|function|async|await|import|export|from|return|if|else|for|while|try|catch|throw|new)\\b/g, '<span class=\"text-purple-400\">$1</span>');\n                // Strings\n                highlightedLine = highlightedLine.replace(/([\"'`])((?:\\\\.|(?!\\1)[^\\\\])*?)\\1/g, '<span class=\"text-green-400\">$1$2$1</span>');\n                // Comments\n                highlightedLine = highlightedLine.replace(/(\\/\\/.*$)/g, '<span class=\"text-gray-500\">$1</span>');\n                // Numbers\n                highlightedLine = highlightedLine.replace(/\\b(\\d+\\.?\\d*)\\b/g, '<span class=\"text-yellow-400\">$1</span>');\n            } else if (lang === 'python') {\n                // Python keywords\n                highlightedLine = highlightedLine.replace(/\\b(import|from|def|class|if|else|elif|for|while|try|except|with|as|return|yield|lambda|and|or|not|in|is|None|True|False)\\b/g, '<span class=\"text-purple-400\">$1</span>');\n                // Strings\n                highlightedLine = highlightedLine.replace(/([\"'`])((?:\\\\.|(?!\\1)[^\\\\])*?)\\1/g, '<span class=\"text-green-400\">$1$2$1</span>');\n                // Comments\n                highlightedLine = highlightedLine.replace(/(#.*$)/g, '<span class=\"text-gray-500\">$1</span>');\n            } else if (lang === 'bash' || lang === 'shell') {\n                // Bash commands\n                highlightedLine = highlightedLine.replace(/\\b(curl|echo|export|cd|ls|mkdir|rm|cp|mv|grep|awk|sed)\\b/g, '<span class=\"text-blue-400\">$1</span>');\n                // Flags\n                highlightedLine = highlightedLine.replace(/(-[a-zA-Z]+)/g, '<span class=\"text-yellow-400\">$1</span>');\n                // Strings\n                highlightedLine = highlightedLine.replace(/([\"'`])((?:\\\\.|(?!\\1)[^\\\\])*?)\\1/g, '<span class=\"text-green-400\">$1$2$1</span>');\n            } else if (lang === 'json') {\n                // JSON keys\n                highlightedLine = highlightedLine.replace(/\"([^\"]+)\":/g, '<span class=\"text-blue-400\">\"$1\"</span>:');\n                // JSON strings\n                highlightedLine = highlightedLine.replace(/:\\s*\"([^\"]*)\"/g, ': <span class=\"text-green-400\">\"$1\"</span>');\n                // JSON numbers\n                highlightedLine = highlightedLine.replace(/:\\s*(\\d+\\.?\\d*)/g, ': <span class=\"text-yellow-400\">$1</span>');\n                // JSON booleans\n                highlightedLine = highlightedLine.replace(/:\\s*(true|false|null)/g, ': <span class=\"text-purple-400\">$1</span>');\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"table-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"table-cell text-gray-500 text-right pr-4 select-none w-8\",\n                        children: index + 1\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"table-cell text-gray-100\",\n                        dangerouslySetInnerHTML: {\n                            __html: highlightedLine || ' '\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this);\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative bg-gray-900 rounded-xl overflow-hidden border border-gray-700 shadow-2xl\",\n        children: [\n            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 px-4 py-3 text-sm text-gray-300 border-b border-gray-700 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-500 uppercase tracking-wide\",\n                        children: language\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: \"p-4 overflow-x-auto text-sm font-mono leading-relaxed\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"table w-full\",\n                            children: highlightSyntax(children, language || 'javascript')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCopy,\n                        className: \"absolute top-3 right-3 p-2 bg-gray-800/80 hover:bg-gray-700 rounded-lg transition-all duration-200 backdrop-blur-sm border border-gray-600\",\n                        title: \"Copy to clipboard\",\n                        children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-4 w-4 text-green-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-4 w-4 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n_s(CodeBlock, \"NE86rL3vg4NVcTTWDavsT0hUBJs=\");\n_c = CodeBlock;\nfunction Alert(param) {\n    let { type, children } = param;\n    const styles = {\n        info: 'bg-blue-900/20 border-blue-500/30 text-blue-200',\n        warning: 'bg-yellow-900/20 border-yellow-500/30 text-yellow-200',\n        tip: 'bg-green-900/20 border-green-500/30 text-green-200'\n    };\n    const icons = {\n        info: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        warning: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        tip: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    };\n    const Icon = icons[type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border rounded-xl p-4 \".concat(styles[type], \" backdrop-blur-sm\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n            lineNumber: 187,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Alert;\nfunction DocsPage() {\n    _s1();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('getting-started');\n    const sections = [\n        {\n            id: 'getting-started',\n            title: 'Getting Started',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: 'authentication',\n            title: 'Authentication',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: 'api-reference',\n            title: 'API Reference',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: 'routing-strategies',\n            title: 'Routing Strategies',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            id: 'configuration',\n            title: 'Configuration',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            id: 'examples',\n            title: 'Examples',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: 'sdks',\n            title: 'SDKs & Libraries',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            id: 'limits',\n            title: 'Limits & Pricing',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80 flex-shrink-0 bg-gray-900 border-r border-gray-800 min-h-screen\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky top-0 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-white mb-2\",\n                                            children: \"RouKey Documentation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Complete guide to integrating and using RouKey's intelligent AI gateway\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"space-y-2\",\n                                    children: sections.map((section)=>{\n                                        const Icon = section.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveSection(section.id),\n                                            className: \"w-full flex items-center gap-3 px-4 py-3 text-sm rounded-xl transition-all duration-200 \".concat(activeSection === section.id ? 'bg-orange-600 text-white shadow-lg shadow-orange-600/25' : 'text-gray-300 hover:text-white hover:bg-gray-800'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 21\n                                                }, this),\n                                                section.title\n                                            ]\n                                        }, section.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 p-4 bg-gray-800 rounded-xl border border-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 text-orange-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-white\",\n                                                    children: \"Quick Start\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400 mb-3\",\n                                            children: \"Get up and running in under 2 minutes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveSection('getting-started'),\n                                            className: \"w-full bg-orange-600 hover:bg-orange-700 text-white text-xs py-2 px-3 rounded-lg transition-colors\",\n                                            children: \"Start Building\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 bg-black text-white overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: [\n                                    activeSection === 'getting-started' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-white mb-6\",\n                                                        children: \"Getting Started with RouKey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                                        children: \"RouKey is an intelligent AI gateway that optimizes your LLM API usage through advanced routing strategies. Get up and running in under 2 minutes.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-orange-900/40 to-orange-800/40 p-6 rounded-xl border border-orange-500/30 backdrop-blur-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white mb-2 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-orange-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Quick Start\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 mb-4\",\n                                                                children: \"Get up and running with RouKey in minutes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setActiveSection('authentication'),\n                                                                className: \"text-orange-400 hover:text-orange-300 font-medium flex items-center gap-1 transition-colors\",\n                                                                children: [\n                                                                    \"Start here \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 294,\n                                                                        columnNumber: 36\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-blue-900/40 to-blue-800/40 p-6 rounded-xl border border-blue-500/30 backdrop-blur-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white mb-2 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"API Reference\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 mb-4\",\n                                                                children: \"Complete API documentation and examples\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setActiveSection('api-reference'),\n                                                                className: \"text-blue-400 hover:text-blue-300 font-medium flex items-center gap-1 transition-colors\",\n                                                                children: [\n                                                                    \"View API docs \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 39\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white mb-6\",\n                                                        children: \"What is RouKey?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"prose prose-gray max-w-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 leading-relaxed text-lg\",\n                                                            children: \"RouKey is an intelligent AI gateway that sits between your application and multiple LLM providers. It automatically routes requests to the most appropriate model based on your configured strategies, providing cost optimization, improved reliability, and enhanced performance.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white mb-6\",\n                                                        children: \"Key Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4 p-6 bg-gray-800/50 rounded-xl border border-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-orange-400 flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-white text-lg mb-2\",\n                                                                                children: \"Intelligent Routing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 330,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-300\",\n                                                                                children: \"AI-powered request classification and optimal model selection\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 331,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4 p-6 bg-gray-800/50 rounded-xl border border-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-blue-400 flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-white text-lg mb-2\",\n                                                                                children: \"Multiple Strategies\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 337,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-300\",\n                                                                                children: \"Fallback, cost-optimized, role-based, and complexity routing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 338,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4 p-6 bg-gray-800/50 rounded-xl border border-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-green-400 flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-white text-lg mb-2\",\n                                                                                children: \"Secure & Reliable\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 344,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-300\",\n                                                                                children: \"Enterprise-grade security with automatic failover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 345,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4 p-6 bg-gray-800/50 rounded-xl border border-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-purple-400 flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-white text-lg mb-2\",\n                                                                                children: \"Cost Optimization\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 351,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-300\",\n                                                                                children: \"Reduce costs by up to 60% with smart routing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/30 p-6 rounded-xl border border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                        children: \"Ready to get started?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 mb-4\",\n                                                        children: \"Follow our quick start guide to integrate RouKey into your application in minutes.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveSection('authentication'),\n                                                        className: \"bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                                                        children: \"Get Started Now\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'authentication' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-white mb-6\",\n                                                        children: \"Authentication\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                                        children: \"Learn how to authenticate with RouKey using API keys.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                type: \"info\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Important:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" RouKey uses the \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-blue-900/50 px-2 py-1 rounded text-blue-300\",\n                                                        children: \"X-API-Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    \" header for authentication. Never use \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-blue-900/50 px-2 py-1 rounded text-blue-300\",\n                                                        children: \"Authorization\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 31\n                                                    }, this),\n                                                    \" header format.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white mb-6\",\n                                                        children: \"Getting Your API Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 text-lg\",\n                                                                children: \"To get started with RouKey, you'll need to create an API key from your dashboard:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                className: \"list-decimal list-inside space-y-3 text-gray-300 ml-4 text-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            \"Sign up for a RouKey account at \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                href: \"https://roukey.online\",\n                                                                                className: \"text-orange-400 hover:text-orange-300 underline\",\n                                                                                children: \"roukey.online\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 394,\n                                                                                columnNumber: 61\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 394,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Navigate to your dashboard and create a configuration\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Add your LLM provider API keys (OpenAI, Anthropic, etc.)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 396,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Generate a user API key for external access\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 397,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            \"Copy your API key (format: \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                className: \"bg-gray-800 px-2 py-1 rounded text-gray-300\",\n                                                                                children: \"rk_live_...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 398,\n                                                                                columnNumber: 56\n                                                                            }, this),\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white mb-6\",\n                                                        children: \"Authentication Methods\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                                        children: \"Method 1: X-API-Key Header (Recommended)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                                        title: \"Using X-API-Key header\",\n                                                                        language: \"bash\",\n                                                                        children: 'curl -X POST \"https://roukey.online/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"X-API-Key: rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [{\"role\": \"user\", \"content\": \"Hello!\"}],\\n    \"stream\": false\\n  }\\''\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                                        children: \"Method 2: Bearer Token\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                                        title: \"Using Authorization Bearer header\",\n                                                                        language: \"bash\",\n                                                                        children: 'curl -X POST \"https://roukey.online/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"Authorization: Bearer rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [{\"role\": \"user\", \"content\": \"Hello!\"}],\\n    \"stream\": false\\n  }\\''\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 421,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                type: \"tip\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best Practice:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" Always use the \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-green-900/50 px-2 py-1 rounded text-green-300\",\n                                                        children: \"X-API-Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 68\n                                                    }, this),\n                                                    \" header method as it's the primary authentication method for RouKey and ensures maximum compatibility.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'api-reference' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-white mb-6\",\n                                                        children: \"API Reference\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                                        children: \"Complete reference for the RouKey API endpoints and parameters.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white mb-6\",\n                                                        children: \"Base URL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Production Base URL\",\n                                                        children: \"https://roukey.online/api/external/v1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white mb-6\",\n                                                        children: \"Chat Completions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 mb-6 text-lg\",\n                                                        children: \"Create a chat completion using RouKey's intelligent routing. This endpoint is fully compatible with OpenAI's API.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800/50 p-6 rounded-xl mb-6 border border-gray-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-green-600 text-white px-3 py-1 rounded-lg text-sm font-medium\",\n                                                                        children: \"POST\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                        className: \"text-gray-100 text-lg\",\n                                                                        children: \"/chat/completions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 466,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"OpenAI-compatible endpoint with RouKey's intelligent routing capabilities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                        children: \"Request Parameters\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-x-auto mb-8\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                            className: \"min-w-full bg-gray-800/50 border border-gray-700 rounded-xl\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                    className: \"bg-gray-700/50\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-6 py-4 text-left text-sm font-medium text-white\",\n                                                                                children: \"Parameter\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 478,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-6 py-4 text-left text-sm font-medium text-white\",\n                                                                                children: \"Type\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 479,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-6 py-4 text-left text-sm font-medium text-white\",\n                                                                                children: \"Required\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 480,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-6 py-4 text-left text-sm font-medium text-white\",\n                                                                                children: \"Description\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 481,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                    className: \"divide-y divide-gray-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-orange-400\",\n                                                                                    children: \"messages\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 486,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"array\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 487,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-green-400\",\n                                                                                    children: \"Yes\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 488,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"Array of message objects\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 489,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-orange-400\",\n                                                                                    children: \"stream\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 492,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"boolean\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 493,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                                                    children: \"No\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 494,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"Enable streaming responses (recommended for multi-role tasks)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 495,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-orange-400\",\n                                                                                    children: \"temperature\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 498,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"number\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 499,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                                                    children: \"No\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 500,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"Sampling temperature (0-2)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 501,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 497,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-orange-400\",\n                                                                                    children: \"max_tokens\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 504,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"integer\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 505,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                                                    children: \"No\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 506,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"Maximum tokens to generate\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 507,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 503,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm font-mono text-orange-400\",\n                                                                                    children: \"role\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 510,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: \"string\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 511,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                                                    children: \"No\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 512,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                    children: 'RouKey-specific role for routing (e.g., \"coding\", \"writing\")'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 513,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 509,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                        children: \"Example Request\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic chat completion\",\n                                                        language: \"json\",\n                                                        children: '{\\n  \"messages\": [\\n    {\"role\": \"user\", \"content\": \"Explain quantum computing\"}\\n  ],\\n  \"stream\": false,\\n  \"temperature\": 0.7,\\n  \"max_tokens\": 500\\n}'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-white mb-4 mt-8\",\n                                                        children: \"Example with Role-Based Routing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Role-based routing request\",\n                                                        language: \"json\",\n                                                        children: '{\\n  \"messages\": [\\n    {\"role\": \"user\", \"content\": \"Write a Python function to sort a list\"}\\n  ],\\n  \"role\": \"coding\",\\n  \"stream\": true,\\n  \"max_tokens\": 1000\\n}'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                        type: \"tip\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Streaming Recommended:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" For complex tasks that may involve multiple roles or require significant processing, use \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                className: \"bg-green-900/50 px-2 py-1 rounded text-green-300\",\n                                                                children: \"stream: true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \" to avoid timeouts and get real-time responses.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'examples' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-white mb-6\",\n                                                        children: \"Examples\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                                        children: \"Practical examples to get you started with RouKey in different programming languages.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white mb-6\",\n                                                        children: \"JavaScript/Node.js\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic chat completion with fetch\",\n                                                        language: \"javascript\",\n                                                        children: \"const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {\\n  method: 'POST',\\n  headers: {\\n    'Content-Type': 'application/json',\\n    'X-API-Key': 'rk_live_your_api_key_here'\\n  },\\n  body: JSON.stringify({\\n    messages: [\\n      { role: 'user', content: 'Explain machine learning in simple terms' }\\n    ],\\n    stream: false,\\n    max_tokens: 500\\n  })\\n});\\n\\nconst data = await response.json();\\nconsole.log(data.choices[0].message.content);\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Streaming response example\",\n                                                        language: \"javascript\",\n                                                        children: \"const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {\\n  method: 'POST',\\n  headers: {\\n    'Content-Type': 'application/json',\\n    'X-API-Key': 'rk_live_your_api_key_here'\\n  },\\n  body: JSON.stringify({\\n    messages: [\\n      { role: 'user', content: 'Write a detailed explanation of quantum computing' }\\n    ],\\n    stream: true,\\n    max_tokens: 1000\\n  })\\n});\\n\\nconst reader = response.body.getReader();\\nconst decoder = new TextDecoder();\\n\\nwhile (true) {\\n  const { done, value } = await reader.read();\\n  if (done) break;\\n\\n  const chunk = decoder.decode(value);\\n  const lines = chunk.split('\\\\n');\\n\\n  for (const line of lines) {\\n    if (line.startsWith('data: ')) {\\n      const data = line.slice(6);\\n      if (data === '[DONE]') return;\\n\\n      try {\\n        const parsed = JSON.parse(data);\\n        const content = parsed.choices[0]?.delta?.content;\\n        if (content) {\\n          process.stdout.write(content);\\n        }\\n      } catch (e) {\\n        // Skip invalid JSON\\n      }\\n    }\\n  }\\n}\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white mb-6\",\n                                                        children: \"Python\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic chat completion with requests\",\n                                                        language: \"python\",\n                                                        children: \"import requests\\nimport json\\n\\nresponse = requests.post(\\n    'https://roukey.online/api/external/v1/chat/completions',\\n    headers={\\n        'Content-Type': 'application/json',\\n        'X-API-Key': 'rk_live_your_api_key_here'\\n    },\\n    json={\\n        'messages': [\\n            {'role': 'user', 'content': 'Explain machine learning in simple terms'}\\n        ],\\n        'stream': False,\\n        'max_tokens': 500\\n    }\\n)\\n\\ndata = response.json()\\nprint(data['choices'][0]['message']['content'])\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Streaming response with requests\",\n                                                        language: \"python\",\n                                                        children: \"import requests\\nimport json\\n\\nresponse = requests.post(\\n    'https://roukey.online/api/external/v1/chat/completions',\\n    headers={\\n        'Content-Type': 'application/json',\\n        'X-API-Key': 'rk_live_your_api_key_here'\\n    },\\n    json={\\n        'messages': [\\n            {'role': 'user', 'content': 'Write a detailed explanation of quantum computing'}\\n        ],\\n        'stream': True,\\n        'max_tokens': 1000\\n    },\\n    stream=True\\n)\\n\\nfor line in response.iter_lines():\\n    if line:\\n        line = line.decode('utf-8')\\n        if line.startswith('data: '):\\n            data = line[6:]\\n            if data == '[DONE]':\\n                break\\n            try:\\n                parsed = json.loads(data)\\n                content = parsed['choices'][0]['delta'].get('content', '')\\n                if content:\\n                    print(content, end='', flush=True)\\n            except json.JSONDecodeError:\\n                continue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white mb-6\",\n                                                        children: \"cURL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic request with cURL\",\n                                                        language: \"bash\",\n                                                        children: 'curl -X POST \"https://roukey.online/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"X-API-Key: rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [\\n      {\"role\": \"user\", \"content\": \"Hello, how are you?\"}\\n    ],\\n    \"stream\": false,\\n    \"max_tokens\": 150\\n  }\\''\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 692,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Role-based routing with cURL\",\n                                                        language: \"bash\",\n                                                        children: 'curl -X POST \"https://roukey.online/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"X-API-Key: rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [\\n      {\"role\": \"user\", \"content\": \"Write a Python function to calculate fibonacci numbers\"}\\n    ],\\n    \"role\": \"coding\",\\n    \"stream\": true,\\n    \"max_tokens\": 500\\n  }\\''\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 705,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                type: \"info\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Need more examples?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" Check out our GitHub repository for complete example applications and integration guides for popular frameworks like React, Vue, and Express.js.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 720,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'routing-strategies' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-white mb-6\",\n                                                        children: \"Routing Strategies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                                        children: \"RouKey offers multiple intelligent routing strategies to optimize your LLM usage.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 732,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                        children: \"Coming Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"Detailed routing strategies documentation is being prepared.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'configuration' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-white mb-6\",\n                                                        children: \"Configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                                        children: \"Learn how to configure RouKey for optimal performance.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                        children: \"Coming Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"Configuration documentation is being prepared.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'sdks' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-white mb-6\",\n                                                        children: \"SDKs & Libraries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 761,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                                        children: \"Official SDKs and community libraries for RouKey.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 760,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                        children: \"Coming Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"SDK documentation is being prepared.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 759,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'limits' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-5xl font-bold text-white mb-6\",\n                                                        children: \"Limits & Pricing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                                        children: \"Understanding RouKey's usage limits and pricing structure.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 777,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                        children: \"Coming Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"Limits and pricing documentation is being prepared.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 774,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, activeSection, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\n_s1(DocsPage, \"x6z6yFCb1AOn1CFzZeI0YdLu3rM=\");\n_c2 = DocsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CodeBlock\");\n$RefreshReg$(_c1, \"Alert\");\n$RefreshReg$(_c2, \"DocsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/docs/page.tsx\n"));

/***/ })

});