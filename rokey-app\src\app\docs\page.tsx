'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  DocumentTextIcon,
  CodeBracketIcon,
  CogIcon,
  BoltIcon,
  KeyIcon,
  ChevronRightIcon,
  ClipboardDocumentIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  SparklesIcon,
  ArrowTopRightOnSquareIcon,
  CircleStackIcon,
  ListBulletIcon,
  BookOpenIcon,
  UserGroupIcon,
  CloudIcon,
  CpuChipIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  BeakerIcon,
  RocketLaunchIcon,
  AcademicCapIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import LandingNavbar from '@/components/landing/LandingNavbar';

interface CodeBlockProps {
  children: string;
  language?: string;
  title?: string;
}

function CodeBlock({ children, language = 'javascript', title }: CodeBlockProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    await navigator.clipboard.writeText(children);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Enhanced VS Code-style syntax highlighting
  const highlightSyntax = (code: string, lang: string) => {
    const lines = code.split('\n');
    return lines.map((line, index) => {
      let highlightedLine = line;

      if (lang === 'javascript' || lang === 'typescript') {
        // Keywords - VS Code purple
        highlightedLine = highlightedLine.replace(
          /\b(const|let|var|function|async|await|import|export|from|return|if|else|for|while|try|catch|throw|new|class|extends|super|this)\b/g,
          '<span class="text-[#C586C0]">$1</span>'
        );
        // Strings - VS Code green
        highlightedLine = highlightedLine.replace(
          /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
          '<span class="text-[#CE9178]">$1$2$1</span>'
        );
        // Comments - VS Code gray-green
        highlightedLine = highlightedLine.replace(
          /(\/\/.*$|\/\*[\s\S]*?\*\/)/g,
          '<span class="text-[#6A9955]">$1</span>'
        );
        // Numbers - VS Code light green
        highlightedLine = highlightedLine.replace(
          /\b(\d+\.?\d*)\b/g,
          '<span class="text-[#B5CEA8]">$1</span>'
        );
        // Functions - VS Code yellow
        highlightedLine = highlightedLine.replace(
          /\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*(?=\()/g,
          '<span class="text-[#DCDCAA]">$1</span>'
        );
      } else if (lang === 'python') {
        // Python keywords - VS Code blue
        highlightedLine = highlightedLine.replace(
          /\b(import|from|def|class|if|else|elif|for|while|try|except|with|as|return|yield|lambda|and|or|not|in|is|None|True|False|async|await)\b/g,
          '<span class="text-[#569CD6]">$1</span>'
        );
        // Strings
        highlightedLine = highlightedLine.replace(
          /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
          '<span class="text-[#CE9178]">$1$2$1</span>'
        );
        // Comments
        highlightedLine = highlightedLine.replace(
          /(#.*$)/g,
          '<span class="text-[#6A9955]">$1</span>'
        );
        // Functions
        highlightedLine = highlightedLine.replace(
          /\b([a-zA-Z_][a-zA-Z0-9_]*)\s*(?=\()/g,
          '<span class="text-[#DCDCAA]">$1</span>'
        );
      } else if (lang === 'bash' || lang === 'shell') {
        // Bash commands - VS Code blue
        highlightedLine = highlightedLine.replace(
          /\b(curl|echo|export|cd|ls|mkdir|rm|cp|mv|grep|awk|sed|npm|yarn|pip|python|node)\b/g,
          '<span class="text-[#569CD6]">$1</span>'
        );
        // Flags - VS Code orange
        highlightedLine = highlightedLine.replace(
          /(-[a-zA-Z-]+)/g,
          '<span class="text-[#FF8C00]">$1</span>'
        );
        // Strings
        highlightedLine = highlightedLine.replace(
          /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
          '<span class="text-[#CE9178]">$1$2$1</span>'
        );
      } else if (lang === 'json') {
        // JSON keys - VS Code light blue
        highlightedLine = highlightedLine.replace(
          /"([^"]+)":/g,
          '<span class="text-[#9CDCFE]">"$1"</span>:'
        );
        // JSON strings
        highlightedLine = highlightedLine.replace(
          /:\s*"([^"]*)"/g,
          ': <span class="text-[#CE9178]">"$1"</span>'
        );
        // JSON numbers
        highlightedLine = highlightedLine.replace(
          /:\s*(\d+\.?\d*)/g,
          ': <span class="text-[#B5CEA8]">$1</span>'
        );
        // JSON booleans
        highlightedLine = highlightedLine.replace(
          /:\s*(true|false|null)/g,
          ': <span class="text-[#569CD6]">$1</span>'
        );
      }

      return (
        <div key={index} className="table-row hover:bg-gray-800/30 transition-colors">
          <span className="table-cell text-gray-500 text-right pr-4 select-none w-12 text-xs">
            {index + 1}
          </span>
          <span
            className="table-cell text-gray-100 pl-2"
            dangerouslySetInnerHTML={{ __html: highlightedLine || ' ' }}
          />
        </div>
      );
    });
  };

  return (
    <div className="relative bg-[#1E1E1E] rounded-xl overflow-hidden border border-gray-700 shadow-2xl my-6">
      {title && (
        <div className="bg-[#2D2D30] px-4 py-3 text-sm text-gray-300 border-b border-gray-700 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="flex gap-1.5">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
            </div>
            <span className="font-medium ml-2">{title}</span>
          </div>
          <span className="text-xs text-gray-500 uppercase tracking-wide bg-gray-700 px-2 py-1 rounded">{language}</span>
        </div>
      )}
      <div className="relative">
        <pre className="p-4 overflow-x-auto text-sm font-mono leading-relaxed bg-[#1E1E1E]">
          <code className="table w-full">
            {highlightSyntax(children, language || 'javascript')}
          </code>
        </pre>
        <button
          onClick={handleCopy}
          className="absolute top-3 right-3 p-2 bg-gray-800/80 hover:bg-orange-600 rounded-lg transition-all duration-200 backdrop-blur-sm border border-gray-600 hover:border-orange-500"
          title="Copy to clipboard"
        >
          {copied ? (
            <CheckIcon className="h-4 w-4 text-green-400" />
          ) : (
            <ClipboardDocumentIcon className="h-4 w-4 text-gray-400 hover:text-white" />
          )}
        </button>
      </div>
    </div>
  );
}

interface AlertProps {
  type: 'info' | 'warning' | 'tip' | 'success';
  children: React.ReactNode;
}

function Alert({ type, children }: AlertProps) {
  const styles = {
    info: 'bg-blue-900/20 border-blue-500/30 text-blue-200',
    warning: 'bg-yellow-900/20 border-yellow-500/30 text-yellow-200',
    tip: 'bg-orange-900/20 border-orange-500/30 text-orange-200',
    success: 'bg-green-900/20 border-green-500/30 text-green-200'
  };

  const icons = {
    info: InformationCircleIcon,
    warning: ExclamationTriangleIcon,
    tip: SparklesIcon,
    success: CheckIcon
  };

  const Icon = icons[type];

  return (
    <div className={`border rounded-xl p-4 ${styles[type]} backdrop-blur-sm my-6`}>
      <div className="flex items-start gap-3">
        <Icon className="h-5 w-5 flex-shrink-0 mt-0.5" />
        <div className="text-sm leading-relaxed">{children}</div>
      </div>
    </div>
  );
}

export default function DocsPage() {
  const [activeSection, setActiveSection] = useState('getting-started');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const sections = [
    { id: 'getting-started', title: 'Getting Started', icon: DocumentTextIcon, description: 'Quick setup and overview' },
    { id: 'authentication', title: 'Authentication', icon: KeyIcon, description: 'API keys and security' },
    { id: 'api-reference', title: 'API Reference', icon: CodeBracketIcon, description: 'Complete API documentation' },
    { id: 'routing-strategies', title: 'Routing Strategies', icon: BoltIcon, description: 'Intelligent routing options' },
    { id: 'multi-role', title: 'Multi-Role Orchestration', icon: UserGroupIcon, description: 'Complex workflow management' },
    { id: 'knowledge-base', title: 'Knowledge Base', icon: AcademicCapIcon, description: 'Document upload and RAG' },
    { id: 'custom-roles', title: 'Custom Roles', icon: CogIcon, description: 'Create specialized roles' },
    { id: 'advanced-features', title: 'Advanced Features', icon: BeakerIcon, description: 'Caching, streaming, optimization' },
    { id: 'examples', title: 'Code Examples', icon: SparklesIcon, description: 'Practical implementation guides' },
    { id: 'pricing', title: 'Pricing & Limits', icon: ListBulletIcon, description: 'Plans and usage limits' },
  ];

  // Close mobile menu when section changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [activeSection]);

  return (
    <div className="min-h-screen bg-black">
      <LandingNavbar />

      {/* Hero Section */}
      <div className="relative bg-gradient-to-br from-black via-gray-900 to-black pt-20 pb-16 overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 opacity-5">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: '60px 60px'
            }}
          ></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
              RouKey <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600">Documentation</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              Complete guide to integrating and mastering RouKey's intelligent AI gateway.
              Get up and running in under 2 minutes.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => setActiveSection('getting-started')}
                className="bg-gradient-to-r from-orange-600 to-orange-700 text-white px-8 py-4 rounded-xl font-semibold hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-200 text-lg"
              >
                <RocketLaunchIcon className="h-5 w-5 inline mr-2" />
                Quick Start Guide
              </button>
              <button
                onClick={() => setActiveSection('api-reference')}
                className="border border-gray-600 text-white px-8 py-4 rounded-xl font-semibold hover:bg-gray-800 transition-all duration-200 text-lg"
              >
                <CodeBracketIcon className="h-5 w-5 inline mr-2" />
                API Reference
              </button>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Main Documentation Content */}
      <div className="relative">
        {/* Mobile Navigation Toggle */}
        <div className="lg:hidden fixed top-24 left-4 z-50">
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="bg-gray-900 border border-gray-700 text-white p-3 rounded-xl shadow-lg hover:bg-gray-800 transition-colors"
          >
            {isMobileMenuOpen ? (
              <XMarkIcon className="h-6 w-6" />
            ) : (
              <Bars3Icon className="h-6 w-6" />
            )}
          </button>
        </div>

        <div className="flex">
          {/* Sidebar Navigation */}
          <AnimatePresence>
            {(isMobileMenuOpen || (typeof window !== 'undefined' && window.innerWidth >= 1024)) && (
              <motion.div
                initial={{ x: -300, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: -300, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="fixed lg:sticky top-0 left-0 w-80 h-screen bg-gray-900/95 backdrop-blur-sm border-r border-gray-800 z-40 lg:z-auto overflow-y-auto"
              >
                <div className="p-6 pt-24 lg:pt-6">
                  <div className="mb-8">
                    <h2 className="text-xl font-bold text-white mb-2">Navigation</h2>
                    <p className="text-gray-400 text-sm">
                      Jump to any section
                    </p>
                  </div>

                  <nav className="space-y-2">
                    {sections.map((section) => {
                      const Icon = section.icon;
                      return (
                        <button
                          key={section.id}
                          onClick={() => setActiveSection(section.id)}
                          className={`w-full flex items-start gap-3 px-4 py-4 text-left rounded-xl transition-all duration-200 ${
                            activeSection === section.id
                              ? 'bg-gradient-to-r from-orange-600 to-orange-700 text-white shadow-lg shadow-orange-600/25'
                              : 'text-gray-300 hover:text-white hover:bg-gray-800'
                          }`}
                        >
                          <Icon className="h-5 w-5 flex-shrink-0 mt-0.5" />
                          <div>
                            <div className="font-medium">{section.title}</div>
                            <div className="text-xs opacity-75 mt-1">{section.description}</div>
                          </div>
                        </button>
                      );
                    })}
                  </nav>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Content Area */}
          <div className="flex-1 min-h-screen bg-black">
            <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:ml-0">
              <motion.div
                key={activeSection}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                className="prose prose-lg max-w-none"
              >
                {activeSection === 'getting-started' && (
                  <div className="space-y-12">
                    {/* Header */}
                    <div className="text-center">
                      <h1 className="text-5xl font-bold text-white mb-6">
                        Getting Started with RouKey
                      </h1>
                      <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
                        RouKey is an intelligent AI gateway that optimizes your LLM API usage through
                        advanced routing strategies, multi-role orchestration, and cost optimization.
                        Get up and running in under 2 minutes.
                      </p>
                    </div>

                    {/* Quick Start Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                      <div className="bg-gradient-to-br from-orange-900/40 to-orange-800/40 p-6 rounded-xl border border-orange-500/30 backdrop-blur-sm">
                        <div className="w-12 h-12 bg-orange-600 rounded-xl flex items-center justify-center mb-4">
                          <RocketLaunchIcon className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="text-lg font-semibold text-white mb-2">2-Minute Setup</h3>
                        <p className="text-gray-300 mb-4">Create account, add API keys, generate RouKey API key</p>
                        <button
                          onClick={() => setActiveSection('authentication')}
                          className="text-orange-400 hover:text-orange-300 font-medium flex items-center gap-1 transition-colors"
                        >
                          Start here <ChevronRightIcon className="h-4 w-4" />
                        </button>
                      </div>

                      <div className="bg-gradient-to-br from-blue-900/40 to-blue-800/40 p-6 rounded-xl border border-blue-500/30 backdrop-blur-sm">
                        <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mb-4">
                          <CodeBracketIcon className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="text-lg font-semibold text-white mb-2">OpenAI Compatible</h3>
                        <p className="text-gray-300 mb-4">Drop-in replacement for OpenAI API with enhanced features</p>
                        <button
                          onClick={() => setActiveSection('api-reference')}
                          className="text-blue-400 hover:text-blue-300 font-medium flex items-center gap-1 transition-colors"
                        >
                          View API docs <ChevronRightIcon className="h-4 w-4" />
                        </button>
                      </div>

                      <div className="bg-gradient-to-br from-purple-900/40 to-purple-800/40 p-6 rounded-xl border border-purple-500/30 backdrop-blur-sm">
                        <div className="w-12 h-12 bg-purple-600 rounded-xl flex items-center justify-center mb-4">
                          <BoltIcon className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="text-lg font-semibold text-white mb-2">Smart Routing</h3>
                        <p className="text-gray-300 mb-4">Intelligent routing strategies for optimal performance</p>
                        <button
                          onClick={() => setActiveSection('routing-strategies')}
                          className="text-purple-400 hover:text-purple-300 font-medium flex items-center gap-1 transition-colors"
                        >
                          Learn routing <ChevronRightIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>

                    {/* What is RouKey */}
                    <div className="bg-gray-900/50 p-8 rounded-2xl border border-gray-700">
                      <h2 className="text-3xl font-bold text-white mb-6">What is RouKey?</h2>
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div>
                          <p className="text-gray-300 leading-relaxed text-lg mb-6">
                            RouKey is an intelligent AI gateway that sits between your application and multiple LLM providers.
                            It automatically routes requests to the most appropriate model based on your configured strategies,
                            providing cost optimization, improved reliability, and enhanced performance.
                          </p>
                          <div className="space-y-4">
                            <div className="flex items-center gap-3">
                              <CheckIcon className="h-5 w-5 text-green-400 flex-shrink-0" />
                              <span className="text-gray-300">Access 300+ AI models through one API</span>
                            </div>
                            <div className="flex items-center gap-3">
                              <CheckIcon className="h-5 w-5 text-green-400 flex-shrink-0" />
                              <span className="text-gray-300">Reduce costs by up to 60% with smart routing</span>
                            </div>
                            <div className="flex items-center gap-3">
                              <CheckIcon className="h-5 w-5 text-green-400 flex-shrink-0" />
                              <span className="text-gray-300">Multi-role orchestration for complex tasks</span>
                            </div>
                            <div className="flex items-center gap-3">
                              <CheckIcon className="h-5 w-5 text-green-400 flex-shrink-0" />
                              <span className="text-gray-300">Built-in failover and reliability</span>
                            </div>
                          </div>
                        </div>
                        <div className="bg-gray-800/50 p-6 rounded-xl">
                          <h3 className="text-xl font-semibold text-white mb-4">Architecture Overview</h3>
                          <div className="space-y-3 text-sm">
                            <div className="flex items-center gap-3">
                              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                              <span className="text-gray-300">Your Application</span>
                            </div>
                            <div className="ml-6 border-l-2 border-gray-600 h-4"></div>
                            <div className="flex items-center gap-3">
                              <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                              <span className="text-gray-300">RouKey Gateway</span>
                            </div>
                            <div className="ml-6 border-l-2 border-gray-600 h-4"></div>
                            <div className="flex items-center gap-3">
                              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                              <span className="text-gray-300">OpenAI, Anthropic, Google...</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Key Features */}
                    <div>
                      <h2 className="text-3xl font-bold text-white mb-8 text-center">Key Features</h2>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div className="bg-gray-800/50 p-6 rounded-xl border border-gray-700 hover:border-orange-500/50 transition-colors">
                          <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4">
                            <BoltIcon className="h-6 w-6 text-white" />
                          </div>
                          <h3 className="font-semibold text-white text-lg mb-2">Intelligent Routing</h3>
                          <p className="text-gray-300">AI-powered request classification and optimal model selection based on content, complexity, and cost.</p>
                        </div>

                        <div className="bg-gray-800/50 p-6 rounded-xl border border-gray-700 hover:border-blue-500/50 transition-colors">
                          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4">
                            <UserGroupIcon className="h-6 w-6 text-white" />
                          </div>
                          <h3 className="font-semibold text-white text-lg mb-2">Multi-Role Orchestration</h3>
                          <p className="text-gray-300">Sequential, supervisor, and hierarchical workflows for complex multi-step tasks.</p>
                        </div>

                        <div className="bg-gray-800/50 p-6 rounded-xl border border-gray-700 hover:border-green-500/50 transition-colors">
                          <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4">
                            <CpuChipIcon className="h-6 w-6 text-white" />
                          </div>
                          <h3 className="font-semibold text-white text-lg mb-2">300+ AI Models</h3>
                          <p className="text-gray-300">Access every major AI model through one unified API with automatic failover.</p>
                        </div>

                        <div className="bg-gray-800/50 p-6 rounded-xl border border-gray-700 hover:border-purple-500/50 transition-colors">
                          <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4">
                            <AcademicCapIcon className="h-6 w-6 text-white" />
                          </div>
                          <h3 className="font-semibold text-white text-lg mb-2">Knowledge Base</h3>
                          <p className="text-gray-300">Upload documents (PDF, TXT, MD) for RAG-powered responses with semantic search.</p>
                        </div>

                        <div className="bg-gray-800/50 p-6 rounded-xl border border-gray-700 hover:border-yellow-500/50 transition-colors">
                          <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center mb-4">
                            <ChartBarIcon className="h-6 w-6 text-white" />
                          </div>
                          <h3 className="font-semibold text-white text-lg mb-2">Cost Optimization</h3>
                          <p className="text-gray-300">Reduce costs by up to 60% with intelligent routing and semantic caching.</p>
                        </div>

                        <div className="bg-gray-800/50 p-6 rounded-xl border border-gray-700 hover:border-red-500/50 transition-colors">
                          <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mb-4">
                            <ShieldCheckIcon className="h-6 w-6 text-white" />
                          </div>
                          <h3 className="font-semibold text-white text-lg mb-2">Enterprise Security</h3>
                          <p className="text-gray-300">Secure API key management, rate limiting, and comprehensive usage analytics.</p>
                        </div>
                      </div>
                    </div>

                    {/* Quick Start CTA */}
                    <div className="bg-gradient-to-r from-orange-900/50 to-orange-800/50 p-8 rounded-2xl border border-orange-500/30 text-center">
                      <h3 className="text-2xl font-semibold text-white mb-4">Ready to get started?</h3>
                      <p className="text-gray-300 mb-6 text-lg">
                        Follow our quick start guide to integrate RouKey into your application in minutes.
                      </p>
                      <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <button
                          onClick={() => setActiveSection('authentication')}
                          className="bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 rounded-xl font-medium transition-colors text-lg"
                        >
                          Get Started Now
                        </button>
                        <button
                          onClick={() => setActiveSection('examples')}
                          className="border border-orange-500 text-orange-400 hover:bg-orange-500/10 px-8 py-3 rounded-xl font-medium transition-colors text-lg"
                        >
                          View Examples
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {activeSection === 'authentication' && (
                  <div className="space-y-12">
                    {/* Header */}
                    <div className="text-center">
                      <h1 className="text-5xl font-bold text-white mb-6">Authentication</h1>
                      <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
                        Learn how to authenticate with RouKey using API keys and get your first request working.
                      </p>
                    </div>

                    <Alert type="tip">
                      <strong>Quick Start:</strong> RouKey uses the <code className="bg-orange-900/50 px-2 py-1 rounded text-orange-300">X-API-Key</code> header for authentication.
                      This is the recommended method for all requests.
                    </Alert>

                    {/* Step-by-step Setup */}
                    <div>
                      <h2 className="text-3xl font-bold text-white mb-8">Step-by-Step Setup</h2>
                      <div className="space-y-8">
                        {/* Step 1 */}
                        <div className="flex gap-6">
                          <div className="flex-shrink-0 w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            1
                          </div>
                          <div className="flex-1">
                            <h3 className="text-xl font-semibold text-white mb-3">Create Your RouKey Account</h3>
                            <p className="text-gray-300 mb-4">
                              Sign up for a RouKey account at <a href="https://roukey.online" className="text-orange-400 hover:text-orange-300 underline">roukey.online</a> and choose your subscription plan.
                            </p>
                            <div className="bg-gray-800/50 p-4 rounded-xl">
                              <p className="text-sm text-gray-400">
                                <strong>Free Tier:</strong> 1 configuration, 3 API keys max, basic routing<br/>
                                <strong>Starter ($19/mo):</strong> 4 configurations, 5 API keys, advanced routing<br/>
                                <strong>Professional ($49/mo):</strong> 20 configurations, 15 API keys, knowledge base
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Step 2 */}
                        <div className="flex gap-6">
                          <div className="flex-shrink-0 w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            2
                          </div>
                          <div className="flex-1">
                            <h3 className="text-xl font-semibold text-white mb-3">Create a Configuration</h3>
                            <p className="text-gray-300 mb-4">
                              Navigate to your dashboard and create a new configuration. This groups your API keys and routing settings.
                            </p>
                            <CodeBlock title="Configuration Example" language="json">
{`{
  "name": "My App Configuration",
  "description": "Production API configuration for my application",
  "routing_strategy": "intelligent_role"
}`}
                            </CodeBlock>
                          </div>
                        </div>

                        {/* Step 3 */}
                        <div className="flex gap-6">
                          <div className="flex-shrink-0 w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            3
                          </div>
                          <div className="flex-1">
                            <h3 className="text-xl font-semibold text-white mb-3">Add Your LLM Provider API Keys</h3>
                            <p className="text-gray-300 mb-4">
                              Add API keys from your LLM providers (OpenAI, Anthropic, Google, etc.) to your configuration.
                            </p>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="bg-gray-800/50 p-4 rounded-xl">
                                <h4 className="font-semibold text-white mb-2">Supported Providers</h4>
                                <ul className="text-sm text-gray-300 space-y-1">
                                  <li>• OpenAI (GPT-4, GPT-3.5)</li>
                                  <li>• Anthropic (Claude)</li>
                                  <li>• Google (Gemini)</li>
                                  <li>• Meta (Llama)</li>
                                  <li>• And 300+ more models</li>
                                </ul>
                              </div>
                              <div className="bg-gray-800/50 p-4 rounded-xl">
                                <h4 className="font-semibold text-white mb-2">Security</h4>
                                <ul className="text-sm text-gray-300 space-y-1">
                                  <li>• Keys encrypted at rest</li>
                                  <li>• Never logged or exposed</li>
                                  <li>• Secure key rotation</li>
                                  <li>• Enterprise-grade security</li>
                                </ul>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Step 4 */}
                        <div className="flex gap-6">
                          <div className="flex-shrink-0 w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            4
                          </div>
                          <div className="flex-1">
                            <h3 className="text-xl font-semibold text-white mb-3">Generate Your RouKey API Key</h3>
                            <p className="text-gray-300 mb-4">
                              Generate a user API key for external access. This key will be used to authenticate your application with RouKey.
                            </p>
                            <div className="bg-gray-800/50 p-4 rounded-xl mb-4">
                              <p className="text-sm text-gray-300">
                                Your API key format: <code className="bg-gray-700 px-2 py-1 rounded text-orange-400">rk_live_xxxxxxxx_xxxxxxxxxxxxxxxxxxxxxxxxx</code>
                              </p>
                            </div>
                            <Alert type="warning">
                              <strong>Important:</strong> Copy your API key immediately after generation. For security reasons,
                              you won't be able to see the full key again. Store it securely in your environment variables.
                            </Alert>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Authentication Methods */}
                    <div>
                      <h2 className="text-3xl font-bold text-white mb-8">Authentication Methods</h2>

                      <div className="space-y-8">
                        <div>
                          <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
                            <CheckIcon className="h-5 w-5 text-green-400" />
                            Method 1: X-API-Key Header (Recommended)
                          </h3>
                          <p className="text-gray-300 mb-4">
                            The primary and recommended authentication method for RouKey. Use this for maximum compatibility and performance.
                          </p>
                          <CodeBlock title="X-API-Key Authentication" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [{"role": "user", "content": "Hello, RouKey!"}],
    "stream": false
  }'`}
                          </CodeBlock>
                        </div>

                        <div>
                          <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
                            <InformationCircleIcon className="h-5 w-5 text-blue-400" />
                            Method 2: Authorization Bearer (Alternative)
                          </h3>
                          <p className="text-gray-300 mb-4">
                            Alternative method for compatibility with some SDKs that expect Bearer token authentication.
                          </p>
                          <CodeBlock title="Bearer Token Authentication" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer rk_live_your_api_key_here" \\
  -d '{
    "messages": [{"role": "user", "content": "Hello, RouKey!"}],
    "stream": false
  }'`}
                          </CodeBlock>
                        </div>
                      </div>
                    </div>

                    {/* First Request */}
                    <div className="bg-gradient-to-r from-green-900/50 to-green-800/50 p-8 rounded-2xl border border-green-500/30">
                      <h2 className="text-2xl font-bold text-white mb-6">Make Your First Request</h2>
                      <p className="text-gray-300 mb-6">
                        Test your setup with this simple request. Replace <code className="bg-green-900/50 px-2 py-1 rounded text-green-300">your_api_key_here</code> with your actual RouKey API key.
                      </p>

                      <CodeBlock title="Your First RouKey Request" language="javascript">
{`const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'rk_live_your_api_key_here'
  },
  body: JSON.stringify({
    messages: [
      { role: 'user', content: 'Hello! Can you explain what RouKey does?' }
    ],
    stream: false,
    max_tokens: 500
  })
});

const data = await response.json();
console.log(data.choices[0].message.content);`}
                      </CodeBlock>

                      <Alert type="success">
                        <strong>Success!</strong> If you get a response, you're all set up! RouKey is now routing your requests
                        intelligently across your configured LLM providers.
                      </Alert>
                    </div>

                    {/* Next Steps */}
                    <div className="text-center">
                      <h2 className="text-2xl font-bold text-white mb-6">What's Next?</h2>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <button
                          onClick={() => setActiveSection('api-reference')}
                          className="bg-gray-800/50 hover:bg-gray-700/50 p-6 rounded-xl border border-gray-700 hover:border-blue-500/50 transition-all text-left"
                        >
                          <CodeBracketIcon className="h-8 w-8 text-blue-400 mb-3" />
                          <h3 className="font-semibold text-white mb-2">Explore the API</h3>
                          <p className="text-gray-300 text-sm">Complete API reference and advanced parameters</p>
                        </button>

                        <button
                          onClick={() => setActiveSection('routing-strategies')}
                          className="bg-gray-800/50 hover:bg-gray-700/50 p-6 rounded-xl border border-gray-700 hover:border-orange-500/50 transition-all text-left"
                        >
                          <BoltIcon className="h-8 w-8 text-orange-400 mb-3" />
                          <h3 className="font-semibold text-white mb-2">Configure Routing</h3>
                          <p className="text-gray-300 text-sm">Set up intelligent routing strategies</p>
                        </button>

                        <button
                          onClick={() => setActiveSection('examples')}
                          className="bg-gray-800/50 hover:bg-gray-700/50 p-6 rounded-xl border border-gray-700 hover:border-purple-500/50 transition-all text-left"
                        >
                          <SparklesIcon className="h-8 w-8 text-purple-400 mb-3" />
                          <h3 className="font-semibold text-white mb-2">View Examples</h3>
                          <p className="text-gray-300 text-sm">Practical code examples and integrations</p>
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {activeSection === 'api-reference' && (
                  <div className="space-y-12">
                    {/* Header */}
                    <div className="text-center">
                      <h1 className="text-5xl font-bold text-white mb-6">API Reference</h1>
                      <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
                        Complete reference for RouKey's API endpoints, parameters, and response formats.
                        Fully compatible with OpenAI's API with enhanced routing capabilities.
                      </p>
                    </div>

                    {/* Base URL */}
                    <div>
                      <h2 className="text-3xl font-bold text-white mb-6">Base URL</h2>
                      <CodeBlock title="Production Base URL">
{`https://roukey.online/api/external/v1`}
                      </CodeBlock>
                      <Alert type="info">
                        <strong>OpenAI Compatibility:</strong> RouKey is a drop-in replacement for OpenAI's API.
                        Simply change the base URL and use your RouKey API key.
                      </Alert>
                    </div>

                    {/* Chat Completions */}
                    <div>
                      <h2 className="text-3xl font-bold text-white mb-6">Chat Completions</h2>
                      <p className="text-gray-300 mb-6 text-lg">
                        Create a chat completion using RouKey's intelligent routing. This endpoint automatically
                        selects the best model based on your routing strategy and request content.
                      </p>

                      <div className="bg-gray-800/50 p-6 rounded-xl mb-8 border border-gray-700">
                        <div className="flex items-center gap-3 mb-3">
                          <span className="bg-green-600 text-white px-3 py-1 rounded-lg text-sm font-medium">POST</span>
                          <code className="text-gray-100 text-lg">/chat/completions</code>
                        </div>
                        <p className="text-gray-400">
                          OpenAI-compatible endpoint with RouKey's intelligent routing, multi-role orchestration,
                          and advanced features like semantic caching and knowledge base integration.
                        </p>
                      </div>

                      {/* Request Parameters */}
                      <h3 className="text-2xl font-semibold text-white mb-6">Request Parameters</h3>
                      <div className="overflow-x-auto mb-8">
                        <table className="min-w-full bg-gray-800/50 border border-gray-700 rounded-xl">
                          <thead className="bg-gray-700/50">
                            <tr>
                              <th className="px-6 py-4 text-left text-sm font-medium text-white">Parameter</th>
                              <th className="px-6 py-4 text-left text-sm font-medium text-white">Type</th>
                              <th className="px-6 py-4 text-left text-sm font-medium text-white">Required</th>
                              <th className="px-6 py-4 text-left text-sm font-medium text-white">Description</th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-700">
                            <tr>
                              <td className="px-6 py-4 text-sm font-mono text-orange-400">messages</td>
                              <td className="px-6 py-4 text-sm text-gray-300">array</td>
                              <td className="px-6 py-4 text-sm text-green-400">Yes</td>
                              <td className="px-6 py-4 text-sm text-gray-300">Array of message objects with role and content</td>
                            </tr>
                            <tr>
                              <td className="px-6 py-4 text-sm font-mono text-orange-400">stream</td>
                              <td className="px-6 py-4 text-sm text-gray-300">boolean</td>
                              <td className="px-6 py-4 text-sm text-gray-500">No</td>
                              <td className="px-6 py-4 text-sm text-gray-300">Enable streaming responses (recommended for complex tasks)</td>
                            </tr>
                            <tr>
                              <td className="px-6 py-4 text-sm font-mono text-orange-400">temperature</td>
                              <td className="px-6 py-4 text-sm text-gray-300">number</td>
                              <td className="px-6 py-4 text-sm text-gray-500">No</td>
                              <td className="px-6 py-4 text-sm text-gray-300">Sampling temperature (0-2). Higher values = more creative</td>
                            </tr>
                            <tr>
                              <td className="px-6 py-4 text-sm font-mono text-orange-400">max_tokens</td>
                              <td className="px-6 py-4 text-sm text-gray-300">integer</td>
                              <td className="px-6 py-4 text-sm text-gray-500">No</td>
                              <td className="px-6 py-4 text-sm text-gray-300">Maximum tokens to generate in response</td>
                            </tr>
                            <tr>
                              <td className="px-6 py-4 text-sm font-mono text-orange-400">role</td>
                              <td className="px-6 py-4 text-sm text-gray-300">string</td>
                              <td className="px-6 py-4 text-sm text-gray-500">No</td>
                              <td className="px-6 py-4 text-sm text-gray-300">RouKey-specific role for routing (e.g., "coding", "writing", "analysis")</td>
                            </tr>
                            <tr>
                              <td className="px-6 py-4 text-sm font-mono text-orange-400">top_p</td>
                              <td className="px-6 py-4 text-sm text-gray-300">number</td>
                              <td className="px-6 py-4 text-sm text-gray-500">No</td>
                              <td className="px-6 py-4 text-sm text-gray-300">Nucleus sampling parameter (0-1)</td>
                            </tr>
                            <tr>
                              <td className="px-6 py-4 text-sm font-mono text-orange-400">frequency_penalty</td>
                              <td className="px-6 py-4 text-sm text-gray-300">number</td>
                              <td className="px-6 py-4 text-sm text-gray-500">No</td>
                              <td className="px-6 py-4 text-sm text-gray-300">Penalty for frequent tokens (-2 to 2)</td>
                            </tr>
                            <tr>
                              <td className="px-6 py-4 text-sm font-mono text-orange-400">presence_penalty</td>
                              <td className="px-6 py-4 text-sm text-gray-300">number</td>
                              <td className="px-6 py-4 text-sm text-gray-500">No</td>
                              <td className="px-6 py-4 text-sm text-gray-300">Penalty for new tokens (-2 to 2)</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      {/* Example Requests */}
                      <h3 className="text-2xl font-semibold text-white mb-6">Example Requests</h3>

                      <div className="space-y-8">
                        <div>
                          <h4 className="text-lg font-semibold text-white mb-4">Basic Chat Completion</h4>
                          <CodeBlock title="Simple request" language="json">
{`{
  "messages": [
    {"role": "user", "content": "Explain quantum computing in simple terms"}
  ],
  "stream": false,
  "temperature": 0.7,
  "max_tokens": 500
}`}
                          </CodeBlock>
                        </div>

                        <div>
                          <h4 className="text-lg font-semibold text-white mb-4">Role-Based Routing</h4>
                          <CodeBlock title="Request with specific role" language="json">
{`{
  "messages": [
    {"role": "user", "content": "Write a Python function to sort a list efficiently"}
  ],
  "role": "coding",
  "stream": true,
  "max_tokens": 1000,
  "temperature": 0.3
}`}
                          </CodeBlock>
                          <Alert type="tip">
                            <strong>Role-Based Routing:</strong> Use the <code className="bg-orange-900/50 px-2 py-1 rounded text-orange-300">role</code> parameter
                            to route requests to models optimized for specific tasks like coding, writing, or analysis.
                          </Alert>
                        </div>

                        <div>
                          <h4 className="text-lg font-semibold text-white mb-4">Multi-Role Complex Task</h4>
                          <CodeBlock title="Complex task with streaming" language="json">
{`{
  "messages": [
    {
      "role": "user",
      "content": "Research the latest trends in AI, write a comprehensive report, and create a Python script to visualize the data"
    }
  ],
  "stream": true,
  "max_tokens": 4000,
  "temperature": 0.5
}`}
                          </CodeBlock>
                          <Alert type="info">
                            <strong>Multi-Role Detection:</strong> RouKey automatically detects complex tasks requiring multiple roles
                            and orchestrates them using sequential, supervisor, or hierarchical workflows.
                          </Alert>
                        </div>
                      </div>
                    </div>

                    {/* Response Format */}
                    <div>
                      <h2 className="text-3xl font-bold text-white mb-6">Response Format</h2>

                      <div className="space-y-8">
                        <div>
                          <h3 className="text-xl font-semibold text-white mb-4">Standard Response</h3>
                          <CodeBlock title="Response format" language="json">
{`{
  "id": "chatcmpl-abc123",
  "object": "chat.completion",
  "created": **********,
  "model": "gpt-4",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Quantum computing is a revolutionary approach to computation..."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 12,
    "completion_tokens": 150,
    "total_tokens": 162
  },
  "roukey_metadata": {
    "routing_strategy": "intelligent_role",
    "selected_provider": "openai",
    "selected_model": "gpt-4",
    "cache_hit": false,
    "processing_time_ms": 1250
  }
}`}
                          </CodeBlock>
                        </div>

                        <div>
                          <h3 className="text-xl font-semibold text-white mb-4">Streaming Response</h3>
                          <CodeBlock title="Streaming response format" language="json">
{`data: {"id":"chatcmpl-abc123","object":"chat.completion.chunk","created":**********,"model":"gpt-4","choices":[{"index":0,"delta":{"content":"Quantum"},"finish_reason":null}]}

data: {"id":"chatcmpl-abc123","object":"chat.completion.chunk","created":**********,"model":"gpt-4","choices":[{"index":0,"delta":{"content":" computing"},"finish_reason":null}]}

data: {"id":"chatcmpl-abc123","object":"chat.completion.chunk","created":**********,"model":"gpt-4","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}

data: [DONE]`}
                          </CodeBlock>
                          <Alert type="tip">
                            <strong>Streaming Benefits:</strong> Use streaming for better user experience, especially for long responses
                            or multi-role tasks. It also helps avoid timeouts on complex requests.
                          </Alert>
                        </div>
                      </div>
                    </div>

                    {/* Async Processing */}
                    <div>
                      <h2 className="text-3xl font-bold text-white mb-6">Async Processing</h2>
                      <p className="text-gray-300 mb-6 text-lg">
                        For very complex tasks that may take longer to process, RouKey offers async processing
                        with webhook notifications.
                      </p>

                      <div className="bg-gray-800/50 p-6 rounded-xl mb-6 border border-gray-700">
                        <div className="flex items-center gap-3 mb-3">
                          <span className="bg-blue-600 text-white px-3 py-1 rounded-lg text-sm font-medium">POST</span>
                          <code className="text-gray-100 text-lg">/async/submit</code>
                        </div>
                        <p className="text-gray-400">
                          Submit a request for async processing. Returns immediately with a job ID.
                        </p>
                      </div>

                      <CodeBlock title="Async request" language="json">
{`{
  "messages": [
    {"role": "user", "content": "Analyze this 50-page document and create a comprehensive report"}
  ],
  "webhook_url": "https://your-app.com/webhook/roukey",
  "max_tokens": 8000
}`}
                      </CodeBlock>

                      <CodeBlock title="Async response" language="json">
{`{
  "job_id": "job_abc123",
  "status": "queued",
  "estimated_completion": "2024-01-15T10:30:00Z",
  "webhook_url": "https://your-app.com/webhook/roukey"
}`}
                      </CodeBlock>
                    </div>

                    {/* Error Handling */}
                    <div>
                      <h2 className="text-3xl font-bold text-white mb-6">Error Handling</h2>

                      <div className="space-y-6">
                        <div>
                          <h3 className="text-xl font-semibold text-white mb-4">Common Error Codes</h3>
                          <div className="overflow-x-auto">
                            <table className="min-w-full bg-gray-800/50 border border-gray-700 rounded-xl">
                              <thead className="bg-gray-700/50">
                                <tr>
                                  <th className="px-6 py-4 text-left text-sm font-medium text-white">Code</th>
                                  <th className="px-6 py-4 text-left text-sm font-medium text-white">Description</th>
                                  <th className="px-6 py-4 text-left text-sm font-medium text-white">Solution</th>
                                </tr>
                              </thead>
                              <tbody className="divide-y divide-gray-700">
                                <tr>
                                  <td className="px-6 py-4 text-sm font-mono text-red-400">401</td>
                                  <td className="px-6 py-4 text-sm text-gray-300">Unauthorized - Invalid API key</td>
                                  <td className="px-6 py-4 text-sm text-gray-300">Check your API key and authentication method</td>
                                </tr>
                                <tr>
                                  <td className="px-6 py-4 text-sm font-mono text-red-400">403</td>
                                  <td className="px-6 py-4 text-sm text-gray-300">Forbidden - Feature not available on your plan</td>
                                  <td className="px-6 py-4 text-sm text-gray-300">Upgrade your subscription plan</td>
                                </tr>
                                <tr>
                                  <td className="px-6 py-4 text-sm font-mono text-red-400">429</td>
                                  <td className="px-6 py-4 text-sm text-gray-300">Rate limit exceeded</td>
                                  <td className="px-6 py-4 text-sm text-gray-300">Implement exponential backoff retry logic</td>
                                </tr>
                                <tr>
                                  <td className="px-6 py-4 text-sm font-mono text-red-400">500</td>
                                  <td className="px-6 py-4 text-sm text-gray-300">Internal server error</td>
                                  <td className="px-6 py-4 text-sm text-gray-300">Retry the request or contact support</td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-xl font-semibold text-white mb-4">Error Response Format</h3>
                          <CodeBlock title="Error response" language="json">
{`{
  "error": {
    "message": "Invalid API key provided",
    "type": "invalid_request_error",
    "code": "invalid_api_key"
  }
}`}
                          </CodeBlock>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Placeholder sections for remaining content */}
                {activeSection === 'routing-strategies' && (
                  <div className="space-y-12">
                    <div className="text-center">
                      <h1 className="text-5xl font-bold text-white mb-6">Routing Strategies</h1>
                      <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
                        RouKey offers 7 intelligent routing strategies to optimize your LLM usage for cost, performance, and accuracy.
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {[
                        { name: 'Default Behavior', desc: 'Automatic load balancing', icon: CogIcon },
                        { name: 'Intelligent Role', desc: 'AI-powered role classification', icon: BoltIcon },
                        { name: 'Complexity Round Robin', desc: 'Route by prompt complexity', icon: ChartBarIcon },
                        { name: 'Auto Optimal', desc: 'Automatic optimization', icon: SparklesIcon },
                        { name: 'Strict Fallback', desc: 'Ordered failover sequence', icon: ShieldCheckIcon },
                        { name: 'Cost Optimized', desc: 'Cost-based routing', icon: ChartBarIcon },
                        { name: 'A/B Routing', desc: 'Split testing capabilities', icon: BeakerIcon }
                      ].map((strategy, index) => (
                        <div key={index} className="bg-gray-800/50 p-6 rounded-xl border border-gray-700 hover:border-orange-500/50 transition-colors">
                          <strategy.icon className="h-8 w-8 text-orange-400 mb-4" />
                          <h3 className="font-semibold text-white text-lg mb-2">{strategy.name}</h3>
                          <p className="text-gray-300">{strategy.desc}</p>
                        </div>
                      ))}
                    </div>

                    <Alert type="info">
                      <strong>Coming Soon:</strong> Detailed routing strategies documentation with configuration examples and use cases.
                    </Alert>
                  </div>
                )}

                {activeSection === 'multi-role' && (
                  <div className="space-y-12">
                    <div className="text-center">
                      <h1 className="text-5xl font-bold text-white mb-6">Multi-Role Orchestration</h1>
                      <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
                        RouKey's advanced orchestration system automatically manages complex workflows involving multiple AI roles.
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="bg-gradient-to-br from-blue-900/40 to-blue-800/40 p-6 rounded-xl border border-blue-500/30">
                        <h3 className="text-lg font-semibold text-white mb-2">Sequential Workflows</h3>
                        <p className="text-gray-300">Tasks executed one after another in a specific order</p>
                      </div>
                      <div className="bg-gradient-to-br from-green-900/40 to-green-800/40 p-6 rounded-xl border border-green-500/30">
                        <h3 className="text-lg font-semibold text-white mb-2">Supervisor Patterns</h3>
                        <p className="text-gray-300">Coordinated execution with oversight and quality control</p>
                      </div>
                      <div className="bg-gradient-to-br from-purple-900/40 to-purple-800/40 p-6 rounded-xl border border-purple-500/30">
                        <h3 className="text-lg font-semibold text-white mb-2">Hierarchical Workflows</h3>
                        <p className="text-gray-300">Complex nested workflows for advanced task management</p>
                      </div>
                    </div>

                    <Alert type="tip">
                      <strong>Automatic Detection:</strong> RouKey uses Gemini classification to automatically detect when tasks require multiple roles and selects the appropriate workflow pattern.
                    </Alert>
                  </div>
                )}

                {activeSection === 'knowledge-base' && (
                  <div className="space-y-12">
                    <div className="text-center">
                      <h1 className="text-5xl font-bold text-white mb-6">Knowledge Base</h1>
                      <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
                        Upload documents and enable RAG-powered responses with semantic search capabilities.
                      </p>
                    </div>

                    <div className="bg-gray-800/50 p-8 rounded-2xl border border-gray-700">
                      <h2 className="text-2xl font-bold text-white mb-6">Supported File Types</h2>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="text-center">
                          <div className="w-16 h-16 bg-red-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <DocumentTextIcon className="h-8 w-8 text-white" />
                          </div>
                          <h3 className="font-semibold text-white mb-2">PDF Files</h3>
                          <p className="text-gray-300 text-sm">Upload PDF documents up to 10MB</p>
                        </div>
                        <div className="text-center">
                          <div className="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <DocumentTextIcon className="h-8 w-8 text-white" />
                          </div>
                          <h3 className="font-semibold text-white mb-2">Text Files</h3>
                          <p className="text-gray-300 text-sm">Plain text files (.txt)</p>
                        </div>
                        <div className="text-center">
                          <div className="w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <DocumentTextIcon className="h-8 w-8 text-white" />
                          </div>
                          <h3 className="font-semibold text-white mb-2">Markdown</h3>
                          <p className="text-gray-300 text-sm">Markdown files (.md)</p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-800/50 p-6 rounded-xl border border-gray-700">
                      <h3 className="text-xl font-semibold text-white mb-4">Document Limits by Plan</h3>
                      <ul className="space-y-2 text-gray-300">
                        <li>• <strong>Free:</strong> Knowledge base not available</li>
                        <li>• <strong>Starter:</strong> Knowledge base not available</li>
                        <li>• <strong>Professional:</strong> Up to 5 documents</li>
                        <li>• <strong>Enterprise:</strong> Unlimited documents</li>
                      </ul>
                    </div>
                  </div>
                )}

                {activeSection === 'custom-roles' && (
                  <div className="space-y-12">
                    <div className="text-center">
                      <h1 className="text-5xl font-bold text-white mb-6">Custom Roles</h1>
                      <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
                        Create specialized roles for your specific use cases and route requests to optimized models.
                      </p>
                    </div>

                    <Alert type="info">
                      <strong>Feature Availability:</strong> Custom roles are available on Starter, Professional, and Enterprise plans.
                    </Alert>
                  </div>
                )}

                {activeSection === 'advanced-features' && (
                  <div className="space-y-12">
                    <div className="text-center">
                      <h1 className="text-5xl font-bold text-white mb-6">Advanced Features</h1>
                      <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
                        Explore RouKey's advanced capabilities including semantic caching, streaming, and performance optimization.
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div className="bg-gray-800/50 p-6 rounded-xl border border-gray-700">
                        <h3 className="text-xl font-semibold text-white mb-4">Semantic Caching</h3>
                        <p className="text-gray-300 mb-4">
                          RouKey uses Jina embeddings to cache similar requests, reducing latency and costs.
                        </p>
                        <ul className="text-sm text-gray-300 space-y-1">
                          <li>• Sub-1ms memory cache hits</li>
                          <li>• Semantic similarity matching</li>
                          <li>• Automatic cache invalidation</li>
                          <li>• Cost reduction up to 60%</li>
                        </ul>
                      </div>

                      <div className="bg-gray-800/50 p-6 rounded-xl border border-gray-700">
                        <h3 className="text-xl font-semibold text-white mb-4">Streaming Responses</h3>
                        <p className="text-gray-300 mb-4">
                          Real-time response streaming for better user experience and timeout prevention.
                        </p>
                        <ul className="text-sm text-gray-300 space-y-1">
                          <li>• Real-time token streaming</li>
                          <li>• Prevents timeout issues</li>
                          <li>• Better user experience</li>
                          <li>• Recommended for complex tasks</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                )}

                {activeSection === 'examples' && (
                  <div className="space-y-12">
                    <div className="text-center">
                      <h1 className="text-5xl font-bold text-white mb-6">Code Examples</h1>
                      <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
                        Practical examples to get you started with RouKey in different programming languages and frameworks.
                      </p>
                    </div>

                    <Alert type="info">
                      <strong>Coming Soon:</strong> Comprehensive code examples for JavaScript, Python, cURL, and popular frameworks.
                    </Alert>
                  </div>
                )}

                {activeSection === 'pricing' && (
                  <div className="space-y-12">
                    <div className="text-center">
                      <h1 className="text-5xl font-bold text-white mb-6">Pricing & Limits</h1>
                      <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
                        Understanding RouKey's subscription tiers, usage limits, and pricing structure.
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                      {[
                        { name: 'Free', price: '$0', configs: '1', keys: '3', features: ['Basic routing', 'Standard support'] },
                        { name: 'Starter', price: '$19', configs: '4', keys: '5', features: ['Advanced routing', '3 custom roles', 'Priority support'] },
                        { name: 'Professional', price: '$49', configs: '20', keys: '15', features: ['Knowledge base (5 docs)', 'Unlimited custom roles', 'Premium support'] },
                        { name: 'Enterprise', price: 'Custom', configs: '∞', keys: '∞', features: ['Unlimited everything', 'Dedicated support', 'Custom integrations'] }
                      ].map((plan, index) => (
                        <div key={index} className="bg-gray-800/50 p-6 rounded-xl border border-gray-700">
                          <h3 className="text-xl font-semibold text-white mb-2">{plan.name}</h3>
                          <div className="text-2xl font-bold text-orange-400 mb-4">{plan.price}<span className="text-sm text-gray-400">/mo</span></div>
                          <ul className="space-y-2 text-sm text-gray-300 mb-4">
                            <li>• {plan.configs} configurations</li>
                            <li>• {plan.keys} API keys max</li>
                            {plan.features.map((feature, i) => (
                              <li key={i}>• {feature}</li>
                            ))}
                          </ul>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
              {activeSection === 'getting-started' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-6">
                      Getting Started with RouKey
                    </h1>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                      RouKey is an intelligent AI gateway that optimizes your LLM API usage through
                      advanced routing strategies. Get up and running in under 2 minutes.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-gradient-to-br from-orange-900/40 to-orange-800/40 p-6 rounded-xl border border-orange-500/30 backdrop-blur-sm">
                      <h3 className="text-lg font-semibold text-white mb-2 flex items-center gap-2">
                        <BoltIcon className="h-5 w-5 text-orange-400" />
                        Quick Start
                      </h3>
                      <p className="text-gray-300 mb-4">Get up and running with RouKey in minutes</p>
                      <button
                        onClick={() => setActiveSection('authentication')}
                        className="text-orange-400 hover:text-orange-300 font-medium flex items-center gap-1 transition-colors"
                      >
                        Start here <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-blue-900/40 to-blue-800/40 p-6 rounded-xl border border-blue-500/30 backdrop-blur-sm">
                      <h3 className="text-lg font-semibold text-white mb-2 flex items-center gap-2">
                        <CodeBracketIcon className="h-5 w-5 text-blue-400" />
                        API Reference
                      </h3>
                      <p className="text-gray-300 mb-4">Complete API documentation and examples</p>
                      <button
                        onClick={() => setActiveSection('api-reference')}
                        className="text-blue-400 hover:text-blue-300 font-medium flex items-center gap-1 transition-colors"
                      >
                        View API docs <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">What is RouKey?</h2>
                    <div className="prose prose-gray max-w-none">
                      <p className="text-gray-300 leading-relaxed text-lg">
                        RouKey is an intelligent AI gateway that sits between your application and multiple LLM providers.
                        It automatically routes requests to the most appropriate model based on your configured strategies,
                        providing cost optimization, improved reliability, and enhanced performance.
                      </p>
                    </div>
                  </div>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">Key Features</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="flex items-start gap-4 p-6 bg-gray-800/50 rounded-xl border border-gray-700">
                        <BoltIcon className="h-8 w-8 text-orange-400 flex-shrink-0 mt-1" />
                        <div>
                          <h3 className="font-semibold text-white text-lg mb-2">Intelligent Routing</h3>
                          <p className="text-gray-300">AI-powered request classification and optimal model selection</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4 p-6 bg-gray-800/50 rounded-xl border border-gray-700">
                        <CogIcon className="h-8 w-8 text-blue-400 flex-shrink-0 mt-1" />
                        <div>
                          <h3 className="font-semibold text-white text-lg mb-2">Multiple Strategies</h3>
                          <p className="text-gray-300">Fallback, cost-optimized, role-based, and complexity routing</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4 p-6 bg-gray-800/50 rounded-xl border border-gray-700">
                        <KeyIcon className="h-8 w-8 text-green-400 flex-shrink-0 mt-1" />
                        <div>
                          <h3 className="font-semibold text-white text-lg mb-2">Secure & Reliable</h3>
                          <p className="text-gray-300">Enterprise-grade security with automatic failover</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4 p-6 bg-gray-800/50 rounded-xl border border-gray-700">
                        <SparklesIcon className="h-8 w-8 text-purple-400 flex-shrink-0 mt-1" />
                        <div>
                          <h3 className="font-semibold text-white text-lg mb-2">Cost Optimization</h3>
                          <p className="text-gray-300">Reduce costs by up to 60% with smart routing</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-800/30 p-6 rounded-xl border border-gray-700">
                    <h3 className="text-xl font-semibold text-white mb-4">Ready to get started?</h3>
                    <p className="text-gray-300 mb-4">
                      Follow our quick start guide to integrate RouKey into your application in minutes.
                    </p>
                    <button
                      onClick={() => setActiveSection('authentication')}
                      className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                    >
                      Get Started Now
                    </button>
                  </div>
                </div>
              )}

              {activeSection === 'authentication' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-6">Authentication</h1>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                      Learn how to authenticate with RouKey using API keys.
                    </p>
                  </div>

                  <Alert type="info">
                    <strong>Important:</strong> RouKey uses the <code className="bg-blue-900/50 px-2 py-1 rounded text-blue-300">X-API-Key</code> header for authentication.
                    Never use <code className="bg-blue-900/50 px-2 py-1 rounded text-blue-300">Authorization</code> header format.
                  </Alert>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">Getting Your API Key</h2>
                    <div className="space-y-4">
                      <p className="text-gray-300 text-lg">
                        To get started with RouKey, you'll need to create an API key from your dashboard:
                      </p>
                      <ol className="list-decimal list-inside space-y-3 text-gray-300 ml-4 text-lg">
                        <li>Sign up for a RouKey account at <a href="https://roukey.online" className="text-orange-400 hover:text-orange-300 underline">roukey.online</a></li>
                        <li>Navigate to your dashboard and create a configuration</li>
                        <li>Add your LLM provider API keys (OpenAI, Anthropic, etc.)</li>
                        <li>Generate a user API key for external access</li>
                        <li>Copy your API key (format: <code className="bg-gray-800 px-2 py-1 rounded text-gray-300">rk_live_...</code>)</li>
                      </ol>
                    </div>
                  </div>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">Authentication Methods</h2>
                    <div className="space-y-8">
                      <div>
                        <h3 className="text-xl font-semibold text-white mb-4">Method 1: X-API-Key Header (Recommended)</h3>
                        <CodeBlock title="Using X-API-Key header" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'`}
                        </CodeBlock>
                      </div>

                      <div>
                        <h3 className="text-xl font-semibold text-white mb-4">Method 2: Bearer Token</h3>
                        <CodeBlock title="Using Authorization Bearer header" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer rk_live_your_api_key_here" \\
  -d '{
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'`}
                        </CodeBlock>
                      </div>
                    </div>
                  </div>

                  <Alert type="tip">
                    <strong>Best Practice:</strong> Always use the <code className="bg-green-900/50 px-2 py-1 rounded text-green-300">X-API-Key</code> header method
                    as it's the primary authentication method for RouKey and ensures maximum compatibility.
                  </Alert>
                </div>
              )}

              {activeSection === 'api-reference' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-6">API Reference</h1>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                      Complete reference for the RouKey API endpoints and parameters.
                    </p>
                  </div>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">Base URL</h2>
                    <CodeBlock title="Production Base URL">
{`https://roukey.online/api/external/v1`}
                    </CodeBlock>
                  </div>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">Chat Completions</h2>
                    <p className="text-gray-300 mb-6 text-lg">
                      Create a chat completion using RouKey's intelligent routing. This endpoint is fully compatible with OpenAI's API.
                    </p>

                    <div className="bg-gray-800/50 p-6 rounded-xl mb-6 border border-gray-700">
                      <div className="flex items-center gap-3 mb-3">
                        <span className="bg-green-600 text-white px-3 py-1 rounded-lg text-sm font-medium">POST</span>
                        <code className="text-gray-100 text-lg">/chat/completions</code>
                      </div>
                      <p className="text-gray-400">
                        OpenAI-compatible endpoint with RouKey's intelligent routing capabilities
                      </p>
                    </div>

                    <h3 className="text-xl font-semibold text-white mb-4">Request Parameters</h3>
                    <div className="overflow-x-auto mb-8">
                      <table className="min-w-full bg-gray-800/50 border border-gray-700 rounded-xl">
                        <thead className="bg-gray-700/50">
                          <tr>
                            <th className="px-6 py-4 text-left text-sm font-medium text-white">Parameter</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-white">Type</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-white">Required</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-white">Description</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-700">
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-orange-400">messages</td>
                            <td className="px-6 py-4 text-sm text-gray-300">array</td>
                            <td className="px-6 py-4 text-sm text-green-400">Yes</td>
                            <td className="px-6 py-4 text-sm text-gray-300">Array of message objects</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-orange-400">stream</td>
                            <td className="px-6 py-4 text-sm text-gray-300">boolean</td>
                            <td className="px-6 py-4 text-sm text-gray-500">No</td>
                            <td className="px-6 py-4 text-sm text-gray-300">Enable streaming responses (recommended for multi-role tasks)</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-orange-400">temperature</td>
                            <td className="px-6 py-4 text-sm text-gray-300">number</td>
                            <td className="px-6 py-4 text-sm text-gray-500">No</td>
                            <td className="px-6 py-4 text-sm text-gray-300">Sampling temperature (0-2)</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-orange-400">max_tokens</td>
                            <td className="px-6 py-4 text-sm text-gray-300">integer</td>
                            <td className="px-6 py-4 text-sm text-gray-500">No</td>
                            <td className="px-6 py-4 text-sm text-gray-300">Maximum tokens to generate</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-orange-400">role</td>
                            <td className="px-6 py-4 text-sm text-gray-300">string</td>
                            <td className="px-6 py-4 text-sm text-gray-500">No</td>
                            <td className="px-6 py-4 text-sm text-gray-300">RouKey-specific role for routing (e.g., "coding", "writing")</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>

                    <h3 className="text-xl font-semibold text-white mb-4">Example Request</h3>
                    <CodeBlock title="Basic chat completion" language="json">
{`{
  "messages": [
    {"role": "user", "content": "Explain quantum computing"}
  ],
  "stream": false,
  "temperature": 0.7,
  "max_tokens": 500
}`}
                    </CodeBlock>

                    <h3 className="text-xl font-semibold text-white mb-4 mt-8">Example with Role-Based Routing</h3>
                    <CodeBlock title="Role-based routing request" language="json">
{`{
  "messages": [
    {"role": "user", "content": "Write a Python function to sort a list"}
  ],
  "role": "coding",
  "stream": true,
  "max_tokens": 1000
}`}
                    </CodeBlock>

                    <Alert type="tip">
                      <strong>Streaming Recommended:</strong> For complex tasks that may involve multiple roles or require significant processing,
                      use <code className="bg-green-900/50 px-2 py-1 rounded text-green-300">stream: true</code> to avoid timeouts and get real-time responses.
                    </Alert>
                  </div>
                </div>
              )}

              {activeSection === 'examples' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-6">Examples</h1>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                      Practical examples to get you started with RouKey in different programming languages.
                    </p>
                  </div>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">JavaScript/Node.js</h2>
                    <CodeBlock title="Basic chat completion with fetch" language="javascript">
{`const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'rk_live_your_api_key_here'
  },
  body: JSON.stringify({
    messages: [
      { role: 'user', content: 'Explain machine learning in simple terms' }
    ],
    stream: false,
    max_tokens: 500
  })
});

const data = await response.json();
console.log(data.choices[0].message.content);`}
                    </CodeBlock>

                    <CodeBlock title="Streaming response example" language="javascript">
{`const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'rk_live_your_api_key_here'
  },
  body: JSON.stringify({
    messages: [
      { role: 'user', content: 'Write a detailed explanation of quantum computing' }
    ],
    stream: true,
    max_tokens: 1000
  })
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;

  const chunk = decoder.decode(value);
  const lines = chunk.split('\\n');

  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = line.slice(6);
      if (data === '[DONE]') return;

      try {
        const parsed = JSON.parse(data);
        const content = parsed.choices[0]?.delta?.content;
        if (content) {
          process.stdout.write(content);
        }
      } catch (e) {
        // Skip invalid JSON
      }
    }
  }
}`}
                    </CodeBlock>
                  </div>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">Python</h2>
                    <CodeBlock title="Basic chat completion with requests" language="python">
{`import requests
import json

response = requests.post(
    'https://roukey.online/api/external/v1/chat/completions',
    headers={
        'Content-Type': 'application/json',
        'X-API-Key': 'rk_live_your_api_key_here'
    },
    json={
        'messages': [
            {'role': 'user', 'content': 'Explain machine learning in simple terms'}
        ],
        'stream': False,
        'max_tokens': 500
    }
)

data = response.json()
print(data['choices'][0]['message']['content'])`}
                    </CodeBlock>

                    <CodeBlock title="Streaming response with requests" language="python">
{`import requests
import json

response = requests.post(
    'https://roukey.online/api/external/v1/chat/completions',
    headers={
        'Content-Type': 'application/json',
        'X-API-Key': 'rk_live_your_api_key_here'
    },
    json={
        'messages': [
            {'role': 'user', 'content': 'Write a detailed explanation of quantum computing'}
        ],
        'stream': True,
        'max_tokens': 1000
    },
    stream=True
)

for line in response.iter_lines():
    if line:
        line = line.decode('utf-8')
        if line.startswith('data: '):
            data = line[6:]
            if data == '[DONE]':
                break
            try:
                parsed = json.loads(data)
                content = parsed['choices'][0]['delta'].get('content', '')
                if content:
                    print(content, end='', flush=True)
            except json.JSONDecodeError:
                continue`}
                    </CodeBlock>
                  </div>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">cURL</h2>
                    <CodeBlock title="Basic request with cURL" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ],
    "stream": false,
    "max_tokens": 150
  }'`}
                    </CodeBlock>

                    <CodeBlock title="Role-based routing with cURL" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Write a Python function to calculate fibonacci numbers"}
    ],
    "role": "coding",
    "stream": true,
    "max_tokens": 500
  }'`}
                    </CodeBlock>
                  </div>

                  <Alert type="info">
                    <strong>Need more examples?</strong> Check out our GitHub repository for complete example applications
                    and integration guides for popular frameworks like React, Vue, and Express.js.
                  </Alert>
                </div>
              )}

              {/* Add placeholder content for other sections */}
              {activeSection === 'routing-strategies' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-6">Routing Strategies</h1>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                      RouKey offers multiple intelligent routing strategies to optimize your LLM usage.
                    </p>
                  </div>
                  <div className="bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center">
                    <h3 className="text-xl font-semibold text-white mb-4">Coming Soon</h3>
                    <p className="text-gray-300">Detailed routing strategies documentation is being prepared.</p>
                  </div>
                </div>
              )}

              {activeSection === 'configuration' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-6">Configuration</h1>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                      Learn how to configure RouKey for optimal performance.
                    </p>
                  </div>
                  <div className="bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center">
                    <h3 className="text-xl font-semibold text-white mb-4">Coming Soon</h3>
                    <p className="text-gray-300">Configuration documentation is being prepared.</p>
                  </div>
                </div>
              )}

              {activeSection === 'sdks' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-6">SDKs & Libraries</h1>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                      Official SDKs and community libraries for RouKey.
                    </p>
                  </div>
                  <div className="bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center">
                    <h3 className="text-xl font-semibold text-white mb-4">Coming Soon</h3>
                    <p className="text-gray-300">SDK documentation is being prepared.</p>
                  </div>
                </div>
              )}

              {activeSection === 'limits' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-6">Limits & Pricing</h1>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                      Understanding RouKey's usage limits and pricing structure.
                    </p>
                  </div>
                  <div className="bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center">
                    <h3 className="text-xl font-semibold text-white mb-4">Coming Soon</h3>
                    <p className="text-gray-300">Limits and pricing documentation is being prepared.</p>
                  </div>
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
